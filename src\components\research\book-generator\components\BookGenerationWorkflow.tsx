import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { 
  BookOpen, 
  CheckCircle, 
  Clock, 
  Edit, 
  RefreshCw, 
  ArrowRight, 
  ArrowLeft,
  FileText,
  Loader2,
  AlertCircle,
  ThumbsUp,
  MessageSquare
} from "lucide-react";
import {
  BookMetadata,
  UserChapter,
  GeneratedChapter,
  GeneratedOutline,
  BookContext,
  Citation
} from '../types';
import type { BookGenerationWorkflow } from '../types';
import { useBookContextStore } from '../stores/book-context.store';
import bookAIService from '../services/book-ai.service';
import { extractCitationsFromText } from '../../paper-generator/citation-extraction.enhanced';
import { ChapterReviewCard } from './ChapterReviewCard';
import { OutlineEditor } from './OutlineEditor';
import { toast } from 'sonner';

interface BookGenerationWorkflowProps {
  bookMetadata: BookMetadata;
  userChapters: UserChapter[];
  selectedModel: string;
  onComplete: (chapters: GeneratedChapter[], citations: Citation[]) => void;
  onBack: () => void;
}

export const BookGenerationWorkflow: React.FC<BookGenerationWorkflowProps> = ({
  bookMetadata,
  userChapters,
  selectedModel,
  onComplete,
  onBack
}) => {
  // Workflow state
  const [workflow, setWorkflow] = useState<BookGenerationWorkflow>({
    currentStep: 'outline-generation',
    currentChapterIndex: 0,
    totalChapters: userChapters.length,
    outlineGenerated: false,
    chaptersCompleted: 0
  });

  // Generated content
  const [generatedOutlines, setGeneratedOutlines] = useState<GeneratedOutline[]>([]);
  const [generatedChapters, setGeneratedChapters] = useState<GeneratedChapter[]>([]);
  const [allCitations, setAllCitations] = useState<Citation[]>([]);

  // UI state
  const [isGenerating, setIsGenerating] = useState(false);
  const [userFeedback, setUserFeedback] = useState('');
  const [currentChapterContent, setCurrentChapterContent] = useState('');

  // Context store
  const {
    initializeBookContext,
    getContextForChapter,
    addChapterContext,
    updateGenerationProgress,
    setChapterOutline,
    getChapterOutline
  } = useBookContextStore();

  // Validate and repair context for chapter generation
  const validateAndRepairContext = async (
    context: BookContext,
    chapterIndex: number,
    chapterId: string
  ): Promise<BookContext> => {
    try {
      // Check if we have the expected number of previous chapters
      const expectedPreviousChapters = chapterIndex;
      const actualPreviousChapters = context.previousChapters.length;

      console.log(`Context validation: Expected ${expectedPreviousChapters} previous chapters, got ${actualPreviousChapters}`);

      if (actualPreviousChapters < expectedPreviousChapters) {
        console.warn(`Missing context for chapter ${chapterId}. Attempting to repair...`);

        // Try to rebuild context from generated chapters
        const previousGeneratedChapters = generatedChapters.filter((ch, idx) =>
          idx < chapterIndex && ch.content && (ch.status === 'content-ready' || ch.status === 'completed')
        );

        console.log(`Found ${previousGeneratedChapters.length} generated chapters to add to context`);

        // Add missing chapters to context
        for (const ch of previousGeneratedChapters) {
          if (!ch.summary) {
            console.log(`Generating missing summary for ${ch.title}`);
            try {
              const { summary, keyPoints } = await bookAIService.generateChapterSummary(
                ch.title,
                ch.content || '',
                { model: selectedModel }
              );

              // Update the chapter with summary
              setGeneratedChapters(prev => prev.map((chapter) =>
                chapter.id === ch.id ? { ...chapter, summary, keyPoints } : chapter
              ));

              // Add to context
              addChapterContext({
                chapterId: ch.id,
                summary,
                keyPoints: keyPoints || [],
                wordCount: ch.wordCount || (ch.content?.split(' ').length || 0),
                generatedAt: new Date()
              });
            } catch (summaryError) {
              console.error(`Failed to generate summary for ${ch.title}:`, summaryError);
              // Add basic context without summary
              addChapterContext({
                chapterId: ch.id,
                summary: `Chapter ${ch.title} - Summary generation failed`,
                keyPoints: [],
                wordCount: ch.wordCount || 0,
                generatedAt: new Date()
              });
            }
          } else {
            // Add existing summary to context
            addChapterContext({
              chapterId: ch.id,
              summary: ch.summary,
              keyPoints: [], // GeneratedChapter doesn't have keyPoints, will be extracted from summary
              wordCount: ch.wordCount || 0,
              generatedAt: new Date()
            });
          }
        }

        // Get updated context after repairs
        const repairedContext = getContextForChapter(chapterId);
        console.log(`Context repaired: Now has ${repairedContext.previousChapters.length} previous chapters`);
        return repairedContext;
      }

      return context;
    } catch (error) {
      console.error('Error validating/repairing context:', error);
      return context; // Return original context if repair fails
    }
  };

  // Initialize context when component mounts
  useEffect(() => {
    const chapterOutlines = userChapters.map(ch => 
      `${ch.outline.title}: ${ch.outline.description}`
    );
    initializeBookContext(bookMetadata, chapterOutlines);
  }, [bookMetadata, userChapters]);

  // Initialize generated chapters
  useEffect(() => {
    const initialChapters = userChapters.map((ch, index) => ({
      id: ch.id,
      title: ch.outline.title,
      description: ch.outline.description,
      status: 'pending' as const,
      icon: BookOpen,
      order: index + 1,
      userApproved: false
    }));
    setGeneratedChapters(initialChapters);
  }, [userChapters]);

  // Step 1: Generate all chapter outlines
  const generateAllOutlines = async () => {
    setIsGenerating(true);
    try {
      toast.info("Generating comprehensive outlines for all chapters...");
      
      const outlines = await bookAIService.generateAllChapterOutlines(
        bookMetadata,
        userChapters,
        { model: selectedModel }
      );
      
      setGeneratedOutlines(outlines);

      // Store outlines in context store
      outlines.forEach(outline => {
        setChapterOutline(outline.chapterId, outline);
      });

      setWorkflow(prev => ({
        ...prev,
        currentStep: 'outline-review',
        outlineGenerated: true
      }));

      // Update chapter statuses
      setGeneratedChapters(prev => prev.map((ch, index) => ({
        ...ch,
        status: 'outline-ready',
        outline: outlines[index]
      })));
      
      toast.success("All chapter outlines generated successfully!");
    } catch (error: any) {
      console.error('Outline generation error:', error);
      toast.error("Failed to generate outlines: " + (error.message || "Please try again"));
    } finally {
      setIsGenerating(false);
    }
  };

  // Step 2: Regenerate specific outline
  const regenerateOutline = async (chapterIndex: number) => {
    setIsGenerating(true);
    try {
      const userChapter = userChapters[chapterIndex];
      const previousChapters = generatedOutlines
        .slice(0, chapterIndex)
        .map(outline => `${outline.title}: ${outline.description}`);

      toast.info(`Regenerating outline for ${userChapter.outline.title}...`);

      const newOutline = await bookAIService.generateChapterOutline(
        bookMetadata,
        userChapter,
        previousChapters,
        { model: selectedModel }
      );

      // Update the specific outline
      setGeneratedOutlines(prev => prev.map((outline, index) =>
        index === chapterIndex ? newOutline : outline
      ));

      // Update chapter status
      setGeneratedChapters(prev => prev.map((ch, index) =>
        index === chapterIndex ? { ...ch, outline: newOutline, status: 'outline-ready' } : ch
      ));

      toast.success("Outline regenerated successfully!");
    } catch (error: any) {
      console.error('Outline regeneration error:', error);
      toast.error("Failed to regenerate outline: " + (error.message || "Please try again"));
    } finally {
      setIsGenerating(false);
    }
  };

  // Save edited outline
  const saveOutline = (chapterIndex: number, updatedOutline: GeneratedOutline) => {
    setGeneratedOutlines(prev => prev.map((outline, index) =>
      index === chapterIndex ? updatedOutline : outline
    ));

    // Update context store
    setChapterOutline(updatedOutline.chapterId, updatedOutline);

    // Update chapter status
    setGeneratedChapters(prev => prev.map((ch, index) =>
      index === chapterIndex ? { ...ch, outline: updatedOutline } : ch
    ));

    toast.success("Outline saved successfully!");
  };

  // Step 3: Approve outlines and start chapter generation
  const approveOutlinesAndStartGeneration = () => {
    setWorkflow(prev => ({
      ...prev,
      currentStep: 'chapter-generation',
      currentChapterIndex: 0
    }));
    
    // Mark all outlines as approved
    setGeneratedOutlines(prev => prev.map(outline => ({
      ...outline,
      status: 'approved'
    })));
    
    generateCurrentChapter();
  };

  // Step 4: Generate current chapter content
  const generateCurrentChapter = async () => {
    const chapterIndex = workflow.currentChapterIndex;
    console.log(`Generating chapter at index: ${chapterIndex}`);
    
    if (chapterIndex >= userChapters.length) {
      console.error(`Invalid chapter index: ${chapterIndex}, max: ${userChapters.length-1}`);
      toast.error("Invalid chapter index");
      return;
    }
    
    const userChapter = userChapters[chapterIndex];
    const outline = generatedOutlines[chapterIndex];
    
    if (!outline) {
      console.error(`No outline found for chapter index ${chapterIndex}`);
      toast.error("No outline found for this chapter");
      return;
    }
    
    setIsGenerating(true);
    
    try {
      toast.info(`Generating Chapter ${chapterIndex + 1}: ${outline.title}...`, {
        duration: 10000
      });
      
      // Update chapter status
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'content-generating' } : ch
      ));
      
      updateGenerationProgress(userChapter.id, 10);
      
      // Get context for this chapter with error recovery
      console.log(`Getting context for chapter ID: ${userChapter.id}`);
      let context = getContextForChapter(userChapter.id);

      // Validate and repair context if needed
      context = await validateAndRepairContext(context, chapterIndex, userChapter.id);

      // Log the context to help with debugging
      console.log(`Previous chapters context: ${context.previousChapters.length} chapters`);
      
      // Verify we have context from previous chapters if this isn't the first chapter
      if (chapterIndex > 0 && context.previousChapters.length === 0) {
        console.warn("Missing context from previous chapters - attempting to rebuild");
        
        // Try to rebuild context from generated chapters
        const previousGeneratedChapters = generatedChapters
          .filter((ch, idx) => idx < chapterIndex && ch.content && ch.title);
        
        if (previousGeneratedChapters.length > 0) {
          console.log(`Rebuilding context from ${previousGeneratedChapters.length} previous chapters`);
          
          // Add each previous chapter to context
          for (const ch of previousGeneratedChapters) {
            if (!ch.summary) {
              console.log(`Generating missing summary for ${ch.title}`);
              const { summary, keyPoints } = await bookAIService.generateChapterSummary(
                ch.title,
                ch.content || '',
                { model: selectedModel }
              );
              
              // Add to context
              addChapterContext({
                chapterId: ch.id,
                summary,
                keyPoints: keyPoints || [],
                wordCount: ch.wordCount || (ch.content?.split(' ').length || 0),
                generatedAt: new Date()
              });
            } else {
              // Add existing summary to context
              addChapterContext({
                chapterId: ch.id,
                summary: ch.summary,
                keyPoints: [],
                wordCount: ch.wordCount || 0,
                generatedAt: new Date()
              });
            }
          }
          
          // Get updated context
          context = getContextForChapter(userChapter.id);
          console.log(`Rebuilt context now has ${context.previousChapters.length} chapters`);
        }
      }
      
      // Compile user content
      const userContent = userChapter.items
        .filter(item => item.content.trim())
        .map(item => `${item.type === 'text' ? 'Text' : 'Figure'}: ${item.content}`)
        .join('\n\n');
      
      updateGenerationProgress(userChapter.id, 30);
      
      // Create enhanced prompt with outline
      const enhancedPrompt = `Write Chapter ${chapterIndex + 1}: "${outline.title}" for the book "${bookMetadata.title}".

Chapter Outline:
${outline.sections.map(section => 
  `${section.order}. ${section.title} (${section.estimatedWordCount} words)
     Description: ${section.description}
     Key Points: ${section.keyPoints.join(', ')}`
).join('\n')}

${context && context.previousChapters.length > 0 ? `
Previous Chapters Context:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
   Summary: ${ch.summary}
   Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}` : ''}

${userContent ? `
User Provided Content:
${userContent}` : ''}

Requirements:
- Follow the detailed outline structure exactly
- Write substantial, book-quality content (${outline.estimatedWordCount} words minimum)
- Use hierarchical headings (## for main sections, ### for subsections)
- Maintain consistency with previous chapters and overall book narrative
- Include relevant examples, case studies, or illustrations where appropriate
- Use a ${bookMetadata.tone} tone throughout
- Include proper citations where academic references would be appropriate
- Target audience: ${bookMetadata.targetAudience}

Write only the main chapter content without the chapter title heading.`;

      updateGenerationProgress(userChapter.id, 50);
      
      console.log(`Calling AI service to generate chapter content...`);
      const content = await bookAIService.generateChapter(enhancedPrompt, {
        model: selectedModel,
        context
      });

      console.log(`Chapter generation completed. Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 100)}...`);
      
      updateGenerationProgress(userChapter.id, 70);
      
      // Generate chapter summary for context
      const { summary, keyPoints } = await bookAIService.generateChapterSummary(
        outline.title,
        content,
        { model: selectedModel }
      );
      
      updateGenerationProgress(userChapter.id, 85);
      
      // Extract citations
      const extractedInfo = extractCitationsFromText(content, userChapter.id);
      if (extractedInfo.citations.length > 0) {
        setAllCitations(prev => [...prev, ...extractedInfo.citations]);
      }
      
      const wordCount = content.split(' ').length;
      
      // Update chapter with generated content
      console.log(`Updating chapter ${chapterIndex} with content length: ${content.length}`);
      setGeneratedChapters(prev => {
        const updated = prev.map((ch, index) =>
          index === chapterIndex ? {
            ...ch,
            status: 'content-ready' as const,
            content,
            summary,
            wordCount,
            citations: extractedInfo.matches
          } : ch
        );
        console.log(`Updated chapter ${chapterIndex} status to content-ready`);
        return updated;
      });
      
      // Add to context - this is critical for passing context to the next chapter
      console.log(`Adding chapter context for: ${userChapter.id}, with summary length: ${summary.length}`);
      const chapterContext = {
        chapterId: userChapter.id,
        summary,
        keyPoints,
        wordCount,
        generatedAt: new Date()
      };
      
      try {
        // Add chapter context for the next chapter to use
        addChapterContext(chapterContext);
        
        // Verify context was added correctly
        const contextInfo = getContextForChapter(userChapter.id);
        console.log(`Context after adding chapter: ${contextInfo.previousChapters.length} previous chapters available`);
        console.log(`Previous chapters in context:`, contextInfo.previousChapters.map(ch => ch.chapterId));
      } catch (error) {
        console.error('Error adding chapter context:', error);
        // Continue anyway - we'll try to recover during chapter approval
      }
      
      console.log(`Setting current chapter content with length: ${content.length}`);
      setCurrentChapterContent(content);
      console.log(`Moving to chapter-review step for chapter ${chapterIndex + 1}`);
      setWorkflow(prev => ({ ...prev, currentStep: 'chapter-review' }));
      
      updateGenerationProgress(userChapter.id, 100);
      
      // Show how many chapters are left
      const remainingChapters = userChapters.length - (chapterIndex + 1);
      if (remainingChapters > 0) {
        toast.success(
          `Chapter ${chapterIndex + 1} generated successfully! ${remainingChapters} chapter${remainingChapters !== 1 ? 's' : ''} remaining.`,
          { duration: 5000 }
        );
      } else {
        toast.success(`Chapter ${chapterIndex + 1} generated successfully! This is the final chapter.`, { duration: 5000 });
      }
      
    } catch (error: any) {
      console.error('Chapter generation error:', error);
      toast.error("Failed to generate chapter: " + (error.message || "Please try again"));
      
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'error' } : ch
      ));
    } finally {
      setIsGenerating(false);
    }
  };

  // Step 5: Approve chapter and move to next
  const approveChapterAndContinue = async () => {
    console.log('Starting chapter approval and continuation process');
    const chapterIndex = workflow.currentChapterIndex;
    
    // Add current chapter to context if it hasn't been done already
    const currentChapter = generatedChapters[chapterIndex];
    const userChapter = userChapters[chapterIndex];
    
    if (!currentChapter || !currentChapter.content) {
      console.error('Cannot approve chapter: missing chapter or content');
      toast.error("Cannot approve chapter: no content available");
      return;
    }
    
    console.log(`Ensuring context for chapter ${chapterIndex + 1} (${currentChapter.id}) is captured`);
    
    try {
      // Always regenerate the summary for consistency
      console.log('Generating/refreshing chapter summary');
      toast.info('Finalizing chapter context...', { id: 'chapter-summary' });
      
      const { summary, keyPoints } = await bookAIService.generateChapterSummary(
        currentChapter.title,
        currentChapter.content,
        { model: selectedModel }
      );
      
      console.log(`Generated summary for ${currentChapter.title}:`, summary.substring(0, 50) + '...');
      
      // Update chapter with summary
      setGeneratedChapters(prev => prev.map((ch, idx) => 
        idx === chapterIndex ? { ...ch, summary, keyPoints: keyPoints || [] } : ch
      ));
      
      // Forcefully add to context store
      const contextToAdd = {
        chapterId: userChapter.id,  // Use consistent ID format
        summary,
        keyPoints: keyPoints || [],
        wordCount: currentChapter.wordCount || currentChapter.content.split(' ').length,
        generatedAt: new Date()
      };
      
      console.log(`Adding context for ${userChapter.id} with summary length: ${summary.length}`);
      addChapterContext(contextToAdd);
      
      // Verify the context was added correctly
      const availableContext = getContextForChapter(userChapter.id);
      console.log(`Available context after addition:`, 
        availableContext.previousChapters.map(ch => ch.chapterId)
      );
      
      toast.success('Chapter context prepared successfully', { id: 'chapter-summary' });
    } catch (error) {
      console.error('Error ensuring chapter context:', error);
      toast.error('Had trouble preparing chapter context, but will try to continue');
      // Continue anyway - we don't want to block the process
    }
    
    // Mark current chapter as approved
    setGeneratedChapters(prev => prev.map((ch, index) => 
      index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
    ));
    
    const nextIndex = chapterIndex + 1;
    
    if (nextIndex < userChapters.length) {
      // Log context state before moving to next chapter
      const nextChapterId = userChapters[nextIndex].id;
      const contextInfo = getContextForChapter(nextChapterId);
      console.log(`Context available for next chapter ${nextIndex + 1} (${nextChapterId}):`, 
        contextInfo.previousChapters.map(ch => `${ch.chapterId}: ${ch.summary.substring(0, 30)}...`)
      );
      
      // Mark current chapter as approved
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
      ));
      
      // Show progress message
      toast.info(`Preparing to generate Chapter ${nextIndex + 1}...`, {
        id: 'next-chapter',
        duration: 3000
      });
      
      // First clear current content and reset UI state
      setCurrentChapterContent('');
      setUserFeedback('');
      
      // Then update workflow state in a separate operation
      setWorkflow(prev => ({
        ...prev,
        currentChapterIndex: nextIndex,
        currentStep: 'chapter-generation',
        chaptersCompleted: prev.chaptersCompleted + 1
      }));
      
      // Important: Schedule the generation of the next chapter after state updates
      // Using a longer timeout to ensure state updates complete
      setTimeout(() => {
        console.log(`Now generating chapter ${nextIndex + 1} (${nextChapterId})`);
        toast.info(`Generating Chapter ${nextIndex + 1}: ${userChapters[nextIndex].outline.title}`, {
          id: 'next-chapter',
          duration: 5000
        });
        
        try {
          // Double-check that context is still available before generating
          const verifyContext = getContextForChapter(nextChapterId);
          console.log(`VERIFY: Next chapter context has ${verifyContext.previousChapters.length} previous chapters`);
          
          if (verifyContext.previousChapters.length < nextIndex) {
            console.warn(`Missing context for next chapter - detected ${verifyContext.previousChapters.length} when should have ${nextIndex}`);
            
            // Final attempt to repair context before generation
            const previousChaptersToAdd = generatedChapters.filter((ch, idx) => 
              idx < nextIndex && ch.content && ch.summary
            );
            
            for (const ch of previousChaptersToAdd) {
              console.log(`Adding emergency context for ${ch.id} with summary: ${ch.summary?.substring(0, 50)}...`);
              addChapterContext({
                chapterId: ch.id,
                summary: ch.summary || `Summary of ${ch.title} (emergency fallback)`,
                keyPoints: [],
                wordCount: ch.wordCount || 0,
                generatedAt: new Date()
              });
            }
          }
          
          generateCurrentChapter();
        } catch (error) {
          console.error('Error during chapter generation:', error);
          toast.error(`Failed to generate Chapter ${nextIndex + 1}. Please try regenerating.`);
        }
      }, 2000); // Increased timeout to ensure state is fully updated
    } else {
      // All chapters completed
      // Mark current chapter as approved first
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
      ));
      
      console.log('All chapters have been completed!');
      
      // Move to completed state
      setWorkflow(prev => ({
        ...prev,
        currentStep: 'completed',
        chaptersCompleted: prev.chaptersCompleted + 1
      }));
      
      // Show completion message
      toast.success('All chapters have been completed! 🎉', {
        duration: 8000
      });
      
      // Call completion handler
      onComplete(generatedChapters, allCitations);
    }
  };

  // Regenerate current chapter with feedback
  const regenerateChapterWithFeedback = async (feedback?: string) => {
    // Add feedback to the generation process
    if (feedback) {
      toast.info("Regenerating chapter with your feedback...");
    } else {
      toast.info("Regenerating chapter...");
    }
    await generateCurrentChapter();
    setUserFeedback('');
  };

  // Handle chapter content editing
  const handleChapterEdit = (content: string) => {
    const chapterIndex = workflow.currentChapterIndex;

    // Update chapter with edited content
    setGeneratedChapters(prev => prev.map((ch, index) =>
      index === chapterIndex ? {
        ...ch,
        content,
        wordCount: content.split(' ').length
      } : ch
    ));

    setCurrentChapterContent(content);
    toast.success("Chapter content updated successfully!");
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'outline-generation':
      case 'outline-review':
        return <FileText className="h-5 w-5" />;
      case 'chapter-generation':
      case 'chapter-review':
        return <BookOpen className="h-5 w-5" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStepTitle = () => {
    switch (workflow.currentStep) {
      case 'outline-generation':
        return 'Generating Chapter Outlines';
      case 'outline-review':
        return 'Review Chapter Outlines';
      case 'chapter-generation':
        return `Generating Chapter ${workflow.currentChapterIndex + 1}`;
      case 'chapter-review':
        return `Review Chapter ${workflow.currentChapterIndex + 1}`;
      case 'completed':
        return 'Book Generation Complete';
      default:
        return 'Book Generation';
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Progress Header */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            {getStepIcon(workflow.currentStep)}
            {getStepTitle()}
          </CardTitle>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round((workflow.chaptersCompleted / workflow.totalChapters) * 100)}%</span>
            </div>
            <Progress value={(workflow.chaptersCompleted / workflow.totalChapters) * 100} className="h-2" />
            <div className="text-xs text-gray-600">
              Chapter {workflow.currentChapterIndex + 1} of {workflow.totalChapters}
              {workflow.currentStep !== 'completed' && workflow.currentStep !== 'outline-generation' && workflow.currentStep !== 'outline-review' && (
                <span> - {generatedOutlines[workflow.currentChapterIndex]?.title || userChapters[workflow.currentChapterIndex]?.outline.title}</span>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      {workflow.currentStep === 'outline-generation' && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Generate Chapter Outlines</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              First, we'll generate comprehensive outlines for all {userChapters.length} chapters. 
              This ensures proper structure and flow throughout your book.
            </p>
            <div className="flex gap-3">
              <Button onClick={generateAllOutlines} disabled={isGenerating} size="lg">
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Outlines...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate All Outlines
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={onBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Chapters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Outline Review Step */}
      {workflow.currentStep === 'outline-review' && (
        <div className="space-y-6">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Review Generated Outlines</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Review the generated outlines below. You can regenerate any outline or proceed to chapter generation.
              </p>
              <div className="flex gap-3">
                <Button onClick={approveOutlinesAndStartGeneration} size="lg">
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Approve & Start Generation
                </Button>
                <Button variant="outline" onClick={onBack}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Chapters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Outline Editors */}
          <div className="grid gap-6">
            {generatedOutlines.map((outline, index) => (
              <OutlineEditor
                key={outline.id}
                outline={outline}
                chapterIndex={index}
                onSave={(updatedOutline) => saveOutline(index, updatedOutline)}
                onRegenerate={() => regenerateOutline(index)}
                isRegenerating={isGenerating}
              />
            ))}
          </div>
        </div>
      )}

      {/* Chapter Generation Step */}
      {workflow.currentStep === 'chapter-generation' && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>
              Generating Chapter {workflow.currentChapterIndex + 1}: {userChapters[workflow.currentChapterIndex]?.outline.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
              <span>AI is writing your chapter with context from previous chapters...</span>
            </div>
            <div className="text-sm text-gray-600">
              This may take several minutes for high-quality, comprehensive content.
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapter Review Step */}
      {workflow.currentStep === 'chapter-review' && (
        <div className="space-y-6">
          <ChapterReviewCard
            chapter={generatedChapters[workflow.currentChapterIndex]}
            chapterIndex={workflow.currentChapterIndex}
            outline={generatedOutlines[workflow.currentChapterIndex]}
            onApprove={approveChapterAndContinue}
            onRegenerate={regenerateChapterWithFeedback}
            onEdit={handleChapterEdit}
            isRegenerating={isGenerating}
          />
        </div>
      )}

      {/* Completion Step */}
      {workflow.currentStep === 'completed' && (
        <>
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-green-600">
                <CheckCircle className="h-6 w-6" />
                Book Generation Complete!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Congratulations! Your book "{bookMetadata.title}" has been successfully generated with {workflow.totalChapters} chapters.
              </p>
              <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{workflow.totalChapters}</div>
                  <div className="text-sm text-gray-600">Chapters</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {generatedChapters.reduce((sum, ch) => sum + (ch.wordCount || 0), 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Words</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{allCitations.length}</div>
                  <div className="text-sm text-gray-600">Citations</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Display all generated chapters */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Chapters Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generatedChapters.map((chapter, index) => (
                  <div key={chapter.id} className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-800">
                          {index + 1}
                        </span>
                        {chapter.title}
                      </h3>
                      <Badge variant={chapter.status === 'completed' ? 'default' : 'secondary'}>
                        {chapter.status === 'completed' ? 'Completed' : chapter.status}
                      </Badge>
                    </div>
                    <p className="text-gray-600">{chapter.description}</p>
                    {chapter.outline && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-700">Sections:</p>
                        <ul className="text-sm text-gray-600 mt-1 pl-5 list-disc">
                          {chapter.outline.sections.slice(0, 5).map(section => (
                            <li key={section.id}>{section.title}</li>
                          ))}
                          {chapter.outline.sections.length > 5 && (
                            <li className="text-gray-500">+ {chapter.outline.sections.length - 5} more sections</li>
                          )}
                        </ul>
                      </div>
                    )}
                    {chapter.wordCount && (
                      <div className="mt-2 text-sm text-gray-500">{chapter.wordCount.toLocaleString()} words</div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};
