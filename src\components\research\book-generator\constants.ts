import {
  BookOpen,
  FileText,
  List,
  Users,
  Target,
  Lightbulb,
  PenTool,
  Search,
  Quote,
  Archive,
  Bookmark,
  Globe,
  Brain,
  Heart,
  Zap,
  Star,
  Award,
  Coffee,
  Compass
} from "lucide-react";
import { BookSectionType } from "./types";

// Book genres and categories
export const BOOK_GENRES = [
  'Fiction',
  'Non-Fiction',
  'Academic',
  'Business',
  'Self-Help',
  'Biography',
  'History',
  'Science',
  'Technology',
  'Health & Wellness',
  'Education',
  'Philosophy',
  'Psychology',
  'Politics',
  'Economics',
  'Art & Culture',
  'Travel',
  'Cooking',
  'Sports',
  'Religion & Spirituality'
];

// Target audiences
export const TARGET_AUDIENCES = [
  'General Public',
  'Academic Researchers',
  'Students (Undergraduate)',
  'Students (Graduate)',
  'Professionals',
  'Industry Experts',
  'Beginners',
  'Intermediate Learners',
  'Advanced Practitioners',
  'Young Adults',
  'Adults',
  'Seniors'
];

// Book tones
export const BOOK_TONES = [
  { id: 'academic', name: 'Academic', description: 'Formal, research-based, scholarly' },
  { id: 'professional', name: 'Professional', description: 'Business-oriented, authoritative' },
  { id: 'casual', name: 'Casual', description: 'Conversational, accessible, friendly' },
  { id: 'narrative', name: 'Narrative', description: 'Story-driven, engaging, personal' }
];

// Book length estimates
export const BOOK_LENGTHS = [
  { id: 'short', name: 'Short (50-100 pages)', wordCount: '15,000-30,000 words', chapters: '5-8 chapters' },
  { id: 'medium', name: 'Medium (100-300 pages)', wordCount: '30,000-90,000 words', chapters: '8-15 chapters' },
  { id: 'long', name: 'Long (300+ pages)', wordCount: '90,000+ words', chapters: '15+ chapters' }
];

// Book section types (including chapters and special sections)
export const BOOK_SECTION_TYPES: BookSectionType[] = [
  // Front matter
  { 
    id: 'preface', 
    name: 'Preface', 
    icon: Quote, 
    color: 'bg-purple-500', 
    description: 'Author\'s introduction to the book',
    order: 1,
    required: false,
    isChapter: false
  },
  { 
    id: 'foreword', 
    name: 'Foreword', 
    icon: Users, 
    color: 'bg-indigo-500', 
    description: 'Introduction by someone other than the author',
    order: 2,
    required: false,
    isChapter: false
  },
  { 
    id: 'introduction', 
    name: 'Introduction', 
    icon: Compass, 
    color: 'bg-blue-500', 
    description: 'Overview and context for the book',
    order: 3,
    required: true,
    isChapter: false
  },
  
  // Main chapters (dynamic - will be generated based on user input)
  { 
    id: 'chapter', 
    name: 'Chapter', 
    icon: BookOpen, 
    color: 'bg-green-500', 
    description: 'Main content chapters',
    order: 10,
    required: true,
    isChapter: true
  },
  
  // Back matter
  { 
    id: 'conclusion', 
    name: 'Conclusion', 
    icon: Target, 
    color: 'bg-orange-500', 
    description: 'Final thoughts and summary',
    order: 90,
    required: true,
    isChapter: false
  },
  { 
    id: 'appendix', 
    name: 'Appendix', 
    icon: Archive, 
    color: 'bg-gray-500', 
    description: 'Additional supporting material',
    order: 91,
    required: false,
    isChapter: false
  },
  { 
    id: 'glossary', 
    name: 'Glossary', 
    icon: Search, 
    color: 'bg-teal-500', 
    description: 'Definitions of key terms',
    order: 92,
    required: false,
    isChapter: false
  },
  { 
    id: 'bibliography', 
    name: 'Bibliography', 
    icon: FileText, 
    color: 'bg-red-500', 
    description: 'Complete list of sources and references',
    order: 93,
    required: false,
    isChapter: false
  },
  { 
    id: 'index', 
    name: 'Index', 
    icon: List, 
    color: 'bg-yellow-500', 
    description: 'Alphabetical listing of topics and page numbers',
    order: 94,
    required: false,
    isChapter: false
  }
];

// AI Models (reusing from paper generator but with book-optimized settings)
export const AI_MODELS = [
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    capabilities: ["Long-form content", "Context awareness", "Academic writing"],
    maxTokens: 8192
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI", 
    capabilities: ["Creative writing", "Structured content", "Research synthesis"],
    maxTokens: 4096
  },
  {
    id: "google/gemini-2.5-flash-lite-preview-06-17",
    name: "Gemini 2.5 Flash",
    provider: "Google",
    capabilities: ["Fast generation", "Multi-modal", "Context retention"],
    maxTokens: 8192
  },
  {
    id: "meta-llama/llama-3.1-405b-instruct",
    name: "Llama 3.1 405B",
    provider: "Meta",
    capabilities: ["Long context", "Detailed analysis", "Technical writing"],
    maxTokens: 4096
  }
];

// Context management settings
export const CONTEXT_SETTINGS = {
  MAX_CONTEXT_TOKENS: 6000, // Reserve tokens for response
  SUMMARY_MAX_WORDS: 400,
  MAX_PREVIOUS_CHAPTERS: 5, // Only keep context from last 5 chapters
  CHAPTER_GENERATION_TOKENS: 4096,
  SUMMARY_GENERATION_TOKENS: 1024
};

// Word count estimates per section type
export const WORD_COUNT_ESTIMATES = {
  preface: { min: 500, max: 1500 },
  foreword: { min: 300, max: 1000 },
  introduction: { min: 1000, max: 3000 },
  chapter: { min: 2000, max: 8000 },
  conclusion: { min: 1000, max: 3000 },
  appendix: { min: 500, max: 2000 },
  glossary: { min: 200, max: 1000 },
  bibliography: { min: 100, max: 500 },
  index: { min: 100, max: 300 }
};

// Chapter numbering styles
export const CHAPTER_NUMBERING_STYLES = [
  { id: 'numeric', name: 'Numeric (1, 2, 3...)', format: (n: number) => n.toString() },
  { id: 'roman', name: 'Roman (I, II, III...)', format: (n: number) => toRoman(n) },
  { id: 'none', name: 'No numbering', format: () => '' }
];

// Helper function for Roman numerals
function toRoman(num: number): string {
  const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
  const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
  let result = '';
  
  for (let i = 0; i < values.length; i++) {
    while (num >= values[i]) {
      result += symbols[i];
      num -= values[i];
    }
  }
  
  return result;
}
