import React from 'react';
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { AIModelOption } from './types';

interface AIModelSelectorProps {
  model: string;
  models: AIModelOption[];
  setModel: (value: string) => void;
  className?: string;
}

// Simple dropdown version to avoid complex UI component issues
export function AIModelSelector({ model, models, setModel, className }: AIModelSelectorProps) {
  // Find the selected model or use the first one as default
  const selectedModel = models.find(m => m.id === model) || models[0];
  
  const handleModelChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setModel(event.target.value);
  };
  
  return (
    <div className={cn("flex flex-col", className)}>
      <div className="relative">
        <select
          value={model}
          onChange={handleModelChange}
          className="w-full appearance-none bg-white/80 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-400 focus:ring-blue-400 rounded-xl py-3 px-4 pr-10 font-medium text-gray-700"
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>
              {m.name} - {m.provider}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <ChevronsUpDown className="h-4 w-4 text-gray-500" />
        </div>
      </div>
      
      {/* Current model details */}
      <div className="mt-2 bg-gray-50 rounded-lg p-3 border border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
          <span className="font-medium">{selectedModel.name}</span>
        </div>
        <div className="mt-1 text-sm text-gray-600">
          <span className="bg-gray-100 px-2 py-0.5 rounded-md">{selectedModel.provider}</span>
          <span className="ml-2">Max tokens: {selectedModel.maxTokens}</span>
        </div>
      </div>
    </div>
  );
}
