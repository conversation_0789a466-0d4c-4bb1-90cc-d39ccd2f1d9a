import {
  <PERSON><PERSON>pen,
  FlaskConical,
  BarChart3,
  Lightbulb,
  PenTool,
  Target,
  FileText,
  Search
} from "lucide-react";
import { SectionType, AIModelOption } from "./types";

// Research paper section types
export const SECTION_TYPES: SectionType[] = [
  { 
    id: 'methodology', 
    name: 'Methodology', 
    icon: FlaskConical, 
    color: 'bg-blue-500', 
    description: 'Research methods and procedures',
    order: 2,
    required: false
  },
  { 
    id: 'results', 
    name: 'Results', 
    icon: BarChart3, 
    color: 'bg-green-500', 
    description: 'Findings and data analysis',
    order: 3,
    required: false
  },
  { 
    id: 'introduction', 
    name: 'Introduction', 
    icon: BookOpen, 
    color: 'bg-purple-500', 
    description: 'Background, problem statement, and objectives',
    order: 1,
    required: false
  },

  { 
    id: 'discussion', 
    name: 'Discussion', 
    icon: Lightbulb, 
    color: 'bg-yellow-500', 
    description: 'Interpretation of results and implications',
    order: 4,
    required: false
  },
  { 
    id: 'conclusion', 
    name: 'Conclusion', 
    icon: PenTool, 
    color: 'bg-red-500', 
    description: 'Summary, limitations, and future work',
    order: 5,
    required: false
  },
  { 
    id: 'abstract', 
    name: 'Abstract', 
    icon: Target, 
    color: 'bg-indigo-500', 
    description: 'Concise summary of the entire research',
    order: 6,
    required: false
  },
  { 
    id: 'keywords', 
    name: 'Keywords', 
    icon: Search, 
    color: 'bg-teal-500', 
    description: 'Key terms representing the paper content',
    order: 0,
    required: false
  },
  { 
    id: 'references', 
    name: 'References', 
    icon: FileText, 
    color: 'bg-gray-500', 
    description: 'Citations and references used throughout the paper',
    order: 7,
    required: false
  }
];

// AI generation prompts for each section
export const SECTION_PROMPTS = {
  metadata: (title: string, field: string, keywords: string[]) => 
    `Paper Title: ${title}
     Research Field: ${field}
     Keywords: ${keywords.join(', ')}`,
  
  introduction: (title: string, field: string, keywords: string[]) =>
    `Create the main body (without any section heading) for the Introduction of a research paper titled "${title}" in ${field}. Keywords: ${keywords.join(', ')}.

     Instructions:
     - Do NOT include a heading like 'Introduction' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Establish context and background
     - Present a clear research problem and motivations
     - Define aims, objectives, and research questions
     - Explain significance and contributions
     - Provide a brief overview of paper structure
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - Each in-text citation must correspond to a real academic paper with genuine authors and publication years
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this paper, we present" or "Here is an introduction to"`,  
  methodology: (title: string, field: string, userContent: string) =>
    `Create the main body (without any section heading) for the Methodology section of a research paper titled "${title}" in ${field}.

     Context (if provided):
     ${userContent}

     Instructions:
     - Do NOT include a heading like 'Methodology' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Detail research design and approach
     - Describe data collection methods and procedures
     - Explain participant selection and sampling strategies if applicable
     - Detail analytical techniques
     - Address ethical considerations
     - Acknowledge limitations
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - All citations should reference real methodological papers, techniques, or frameworks
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this methodology section" or "Here are the methods used"`,
  
  results: (title: string, methodologyContent: string, userContent: string) => 
    `Create the main body (without any section heading) for the Results section of a research paper titled "${title}".
     
     Methodology context:
     ${methodologyContent}
     
     Results context (if provided):
     ${userContent}
     
     Instructions:
     - Do NOT include a heading like 'Results' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Present findings objectively without interpretation
     - Organize results logically based on the methodology
     - Include appropriate statistical analyses
     - Reference tables and figures in academic style
     - Use in-text citations (Author, Year) format if comparing with other studies
     - Use a variety of sources and avoid repeating the same citation
     - DO NOT include a References section at the end; all references will be compiled separately
     - Highlight key findings addressing research questions
     - Avoid phrases like "In this section" or "Here are the results"`,
  
  discussion: (title: string, resultsContent: string, methodologyContent: string) =>
    `Create the main body (without any section heading) for the Discussion section of a research paper titled "${title}".

     Methodology context:
     ${methodologyContent}

     Results context:
     ${resultsContent}

     Instructions:
     - Do NOT include a heading like 'Discussion' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Interpret results in context of research questions
     - Compare findings with previous research
     - Explain unexpected results
     - Discuss theoretical and practical implications
     - Acknowledge limitations
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - Use citations from real academic papers with accurate author names and publication years
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this discussion" or "Here, we discuss"`,
  
  conclusion: (title: string, resultsContent: string, discussionContent: string) =>
    `Create the main body (without any section heading) for the Conclusion section of a research paper titled "${title}".
     
     Results context:
     ${resultsContent}
     
     Discussion context:
     ${discussionContent}
     
     Instructions:
     - Do NOT include a heading like 'Conclusion' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Summarize key findings concisely
     - Revisit research objectives
     - Discuss broader implications for the field
     - Suggest directions for future research
     - Use minimal in-text citations, focusing on synthesizing the work
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In conclusion" or "To summarize"`,
  
  abstract: (title: string, introContent: string, methodologyContent: string, resultsContent: string, conclusionContent: string) =>
    `Create the main body (without any section heading) for the Abstract of a research paper titled "${title}".
     
     Context from paper sections:
     - Introduction: ${introContent}
     - Methodology: ${methodologyContent}
     - Results: ${resultsContent}
     - Conclusion: ${conclusionContent}
     
     Instructions:
     - Do NOT include a heading like 'Abstract' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - 200-250 words maximum
     - Present background context and research problem
     - Briefly describe methodology
     - Highlight key findings
     - State main conclusions and implications
     - Do not include citations in the abstract
     - Write as a single paragraph without headings or bullet points
     - Avoid phrases like "This abstract presents" or "In this study"`,
     
  references: (introContent: string, methodContent: string, resultsContent: string, discussionContent: string, conclusionContent: string) =>
    `Create a complete and properly formatted references section for an academic research paper.

    Analyze the following content from the paper to extract all citations that need references:

    Introduction:
    ${introContent}

    Methodology:
    ${methodContent}

    Results:
    ${resultsContent}

    Discussion:
    ${discussionContent}

    Conclusion:
    ${conclusionContent}

    CRITICAL INSTRUCTIONS FOR APA 7TH EDITION FORMAT:
    - Extract ALL in-text citations in the format (Author, Year) or (Author et al., Year) from the provided sections.
    - For each citation found, create a complete, realistic academic reference in strict APA 7th edition format.
    - EXACT APA FORMAT: Author, A. A. (Year). Title of article. Title of Journal, Volume(Issue), pages. https://doi.org/xx.xxxx/xxxx
    - Use proper capitalization: Only first word of title, first word after colon, and proper nouns capitalized
    - Journal names should be in italics (use realistic journal names appropriate to the research field)
    - Examples of good journal names: "Remote Sensing of Environment", "IEEE Transactions on Geoscience and Remote Sensing", "Journal of Applied Geophysics", "International Journal of Remote Sensing"
    - Create realistic article titles that match the research topic and citation context
    - Use realistic DOIs in format: https://doi.org/10.1016/j.rse.2023.xxxxx
    - Alphabetize references by first author's last name
    - Do not number the references
    - Use hanging indent format (first line flush left, subsequent lines indented)
    - Ensure each citation in the text has a corresponding reference entry
    - Use different journals and publishers for variety
    - Make references look authentic and professionally formatted
    - DO NOT include any title, heading, explanatory text, or instructions in your output
    - Your response should start directly with the first reference entry
    - If no citations are found, return: "No citations found in the paper content."`
};

// Text placeholders for content sections
export const TEXT_PLACEHOLDERS = {
  Methodology:
    "Describe the step-by-step procedures, materials, and data collection methods used in your study...",
  Results:
    "Summarize the key findings, data trends, and statistical analyses that emerged from your research...",
  Introduction:
    "Provide background information and clearly state your research objectives and questions...",

  Discussion:
    "Interpret the significance of your results and relate them back to your methodology and objectives...",
  Conclusion:
    "Highlight the major takeaways from your work and suggest possible future research directions...",
  Abstract:
    "Provide a concise summary of your research problem, methodology, findings, and implications...",
  Keywords:
    "Enter key terms that represent the main concepts in your research paper...",
  References:
    "All in-text citations from the paper will be automatically extracted and formatted into a properly formatted reference list here..."
};

// Figure placeholders for content sections
export const FIGURE_PLACEHOLDERS = {
  Methodology:
    "Explain what this figure illustrates about your experimental setup or workflow...",
  Results:
    "Describe how this visual represents your data findings or trends...",
  Introduction:
    "Use this caption to provide context that supports your research background...",

  Discussion:
    "Connect this figure to the implications and insights drawn from your results...",
  Conclusion:
    "Optionally summarize key points or future work shown in the figure..."
};

// Empty section prompts
export const EMPTY_SECTION_PROMPTS = {
  Methodology:
    'Add step-by-step procedures, materials used, and any figures illustrating your setup.',
  Results:
    'Include data analyses, tables, figures, and key observations from your study.',
  Introduction:
    'Provide background information, objectives, and any initial figures that frame the study.',

  Discussion:
    'Interpret your findings, compare with prior work, and use figures to highlight important trends.',
  Conclusion:
    'Summarize core findings and suggest future directions or applications.',
  Abstract:
    'Provide a concise overview of your research problem, approach, findings and implications.',
  Keywords:
    'Add 4-6 key terms that represent the main concepts in your research.'
};

// Default prompts for text and figure content items
export const DEFAULT_TEXT_PROMPT = `Enter your detailed analysis here...

• Describe your methodology, findings, or observations
• Include statistical data, measurements, or qualitative insights
• Explain the significance and implications of your work
• Reference any related studies or theoretical frameworks`;

export const DEFAULT_FIGURE_PROMPT = `Provide detailed analysis of this figure...

• What does this figure show or demonstrate?
• Key findings, trends, or patterns visible in the data
• Statistical significance or notable observations
• How does this relate to your research objectives?
• Limitations or considerations when interpreting this data
• Comparison with existing literature or expected results

💡 Tip: Upload your figure with a title and click 'Generate AI Analysis' for automatic analysis!`;

// Available AI models
export const AI_MODELS: AIModelOption[] = [
  {
    id: "google/gemini-2.5-flash-lite-preview-06-17",
    name: "Gemini 2.5 Flash",
    provider: "Google",
    capabilities: ["text", "image analysis"],
    maxTokens: 512
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    capabilities: ["text", "image analysis"],
    maxTokens: 1024
  }
];
