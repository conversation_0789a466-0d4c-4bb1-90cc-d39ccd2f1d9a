import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Bot, FileText, Clock, CheckCircle, Download, Edit, Loader2, BookOpen } from "lucide-react";
import { GeneratedSection, PaperMetadata, Citation } from './types';
import { AccordionSectionCard } from './AccordionSectionCard';
import { ExportDialog } from './ExportDialog';
import { SectionCitationDisplay } from './SectionCitationDisplay';
import { CitationDisplay } from './CitationDisplay';

interface GenerationPanelProps {
  generatedSections: GeneratedSection[];
  isGenerating: boolean;
  onExport: () => void;
  onEditInEditor: () => void;
  paperMetadata: PaperMetadata;
  allCitations?: Citation[];
  sectionCitations?: Record<string, string[]>;
}

export const GenerationPanel: React.FC<GenerationPanelProps> = ({
  generatedSections,
  isGenerating,
  onExport,
  onEditInEditor,
  paperMetadata,
  allCitations = [],
  sectionCitations = {}
}) => {
  const completedSections = generatedSections.filter(s => s.status === 'completed');
  const allCompleted = generatedSections.every(s => s.status === 'completed');

  return (
    <div className="grid lg:grid-cols-2 gap-8">
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            Generation Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {generatedSections.map((section) => (
              <div key={section.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-xl bg-white hover:shadow-sm transition-all duration-200">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100">
                    <section.icon className="h-5 w-5 text-gray-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">{section.name}</h3>
                  <p className="text-sm text-gray-500">{section.description}</p>
                </div>
                <div className="flex items-center">
                  {section.status === 'pending' && (
                    <Badge variant="outline" className="flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      Pending
                    </Badge>
                  )}
                  {section.status === 'generating' && (
                    <Badge className="bg-blue-500 hover:bg-blue-600 flex items-center gap-2">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Generating...
                    </Badge>
                  )}
                  {section.status === 'completed' && (
                    <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-2">
                      <CheckCircle className="h-3 w-3" />
                      Complete
                    </Badge>
                  )}
                  {section.status === 'error' && (
                    <Badge className="bg-red-500 hover:bg-red-600 flex items-center gap-2">
                      <CheckCircle className="h-3 w-3" />
                      Error
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-green-500 to-purple-500 rounded-full"></div>
            Generated Content
          </CardTitle>
          <CardDescription className="text-lg">
            {completedSections.length === 0
              ? "Watch as AI generates your paper sections"
              : `Review the ${completedSections.length} generated section${completedSections.length !== 1 ? 's' : ''}`}
          </CardDescription>
          
          {/* Citation statistics */}
          {allCitations.length > 0 && !isGenerating && (
            <div className="mt-2 flex flex-wrap gap-3">
              <Badge variant="outline" className="bg-blue-50">
                <BookOpen className="h-3 w-3 mr-1" /> 
                {allCitations.length} Citations
              </Badge>
              <Badge variant="outline" className="bg-green-50">
                <FileText className="h-3 w-3 mr-1" /> 
                {allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} References
              </Badge>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px] pr-4">
            <div className="space-y-6">
              {completedSections.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[400px] text-center">
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-16 w-16 text-blue-500 animate-spin mb-6" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">Generating Your Research Paper</h3>
                      <p className="text-gray-500 max-w-md">
                        Our AI is carefully analyzing your input and crafting each section. This may take a few moments...
                      </p>
                    </>
                  ) : (
                    <>
                      <Bot className="h-16 w-16 text-gray-400 mb-6" />
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">Ready to Generate</h3>
                      <p className="text-gray-500 max-w-md">
                        Once generation begins, you'll see your paper sections appear here one by one.
                      </p>
                    </>
                  )}
                </div>
              ) : (
                completedSections.map((section) => (
                  <AccordionSectionCard 
                    key={section.id} 
                    section={section} 
                    allCitations={allCitations} 
                    sectionCitations={sectionCitations} 
                  />
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {!isGenerating && allCompleted && completedSections.length > 0 && (
        <div className="text-center mt-12 col-span-2">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">🎉 Your Research Paper is Ready!</h2>
            <div className="flex justify-center gap-4">
              <ExportDialog 
                metadata={paperMetadata} 
                generatedSections={generatedSections} 
                onEditInEditor={onEditInEditor} 
              />
              <Button 
                variant="outline" 
                size="lg"
                className="px-8 py-4 border-2 hover:shadow-lg transition-all duration-200"
                onClick={onEditInEditor}
              >
                <Edit className="h-5 w-5 mr-2" />
                Edit in Main Editor
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
