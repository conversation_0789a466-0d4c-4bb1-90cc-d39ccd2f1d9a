import { BookMetadata, BookContext, ChapterOutline, UserChapter } from './types';

// Outline generation prompts
export const OUTLINE_GENERATION_PROMPTS = {
  // Generate comprehensive outlines for all chapters
  generateAllOutlines: (metadata: BookMetadata, userChapters: UserChapter[]) =>
    `Generate comprehensive, detailed outlines for all chapters of the book "${metadata.title}".

Book Information:
- Genre: ${metadata.genre}
- Target Audience: ${metadata.targetAudience}
- Tone: ${metadata.tone}
- Description: ${metadata.description}

User-Provided Chapters:
${userChapters.map((ch, index) =>
  `Chapter ${index + 1}: ${ch.outline.title}
  Description: ${ch.outline.description}
  User Content: ${ch.items.map(item => item.content).join(' | ')}
  Estimated Words: ${ch.outline.estimatedWordCount}`
).join('\n\n')}

Requirements:
- Create detailed outlines for each chapter with 8-15 main sections
- Each section should have 2-4 subsections
- Include key points and concepts for each section
- Ensure logical flow between chapters
- Maintain consistency with the book's tone and target audience
- Incorporate user-provided content where relevant
- Provide realistic word count estimates for each section

CRITICAL REQUIREMENT: Your response must be a valid parseable JSON object ONLY, with NO markdown formatting, NO code blocks, and NO explanations.

CORRECT RESPONSE FORMAT:
{
  "chapters": [
    {
      "chapterNumber": 1,
      "title": "Chapter Title",
      "description": "Brief chapter description",
      "sections": [
        {
          "title": "Section Title",
          "description": "What this section covers",
          "level": 1,
          "order": 1,
          "estimatedWordCount": 500,
          "keyPoints": ["Point 1", "Point 2", "Point 3"]
        },
        {
          "title": "Another Section Title",
          "description": "Another section description",
          "level": 1,
          "order": 2,
          "estimatedWordCount": 600,
          "keyPoints": ["Point 1", "Point 2", "Point 3"]
        }
      ],
      "estimatedWordCount": 3000,
      "keyPoints": ["Main chapter takeaway 1", "Main chapter takeaway 2"]
    }
  ]
}

WRONG FORMAT EXAMPLES (DO NOT DO THESE):
- DO NOT wrap the JSON in code blocks like \`\`\`json {...} \`\`\`
- DO NOT include explanations like "Here is the JSON:" before or after the JSON
- DO NOT use single quotes instead of double quotes for JSON properties or values
- DO NOT add comments within the JSON
- DO NOT include any text outside the JSON object

THE ENTIRE RESPONSE MUST BE VALID JSON WITH NO OTHER TEXT OR FORMATTING.`,

  // Generate outline for a single chapter
  generateChapterOutline: (metadata: BookMetadata, userChapter: UserChapter, previousChapters: string[]) =>
    `Generate a comprehensive, detailed outline for Chapter ${userChapter.outline.order}: "${userChapter.outline.title}".

Book Context:
- Title: ${metadata.title}
- Genre: ${metadata.genre}
- Target Audience: ${metadata.targetAudience}
- Tone: ${metadata.tone}

Chapter Information:
- Title: ${userChapter.outline.title}
- Description: ${userChapter.outline.description}
- Estimated Word Count: ${userChapter.outline.estimatedWordCount}
- User Content: ${userChapter.items.map(item => `${item.type}: ${item.content}`).join('\n')}

Previous Chapters Context:
${previousChapters.length > 0 ? previousChapters.join('\n') : 'This is the first chapter.'}

Requirements:
- Create 8-15 main sections with descriptive titles
- Each section should have 2-4 subsections
- Include specific key points and concepts for each section
- Ensure logical flow and progression
- Incorporate user-provided content naturally
- Maintain consistency with book tone and audience
- Provide realistic word count estimates

CRITICAL REQUIREMENT: Your response must be a valid parseable JSON object ONLY, with NO markdown formatting, NO code blocks, and NO explanations.

CORRECT RESPONSE FORMAT:
{
  "title": "Chapter Title",
  "description": "Chapter description",
  "sections": [
    {
      "title": "Section Title",
      "description": "What this section covers",
      "level": 1,
      "order": 1,
      "estimatedWordCount": 400,
      "keyPoints": ["Key point 1", "Key point 2"],
      "subsections": [...]
    }
  ],
  "estimatedWordCount": 3000,
  "keyPoints": ["Main takeaway 1", "Main takeaway 2"]
}`
};

// Book section generation prompts
export const BOOK_SECTION_PROMPTS = {
  metadata: (metadata: BookMetadata) => 
    `Book Title: ${metadata.title}
     ${metadata.subtitle ? `Subtitle: ${metadata.subtitle}` : ''}
     Genre: ${metadata.genre}
     Target Audience: ${metadata.targetAudience}
     Tone: ${metadata.tone}
     Keywords: ${metadata.keywords.join(', ')}
     Description: ${metadata.description}`,

  preface: (metadata: BookMetadata) =>
    `Write a compelling preface for the book "${metadata.title}" in the ${metadata.genre} genre, targeting ${metadata.targetAudience}. 
    
    The preface should:
    - Explain why you wrote this book and your personal connection to the topic
    - Set expectations for what readers will gain
    - Acknowledge key influences or inspirations
    - Use a ${metadata.tone} tone throughout
    - Be approximately 500-1000 words
    
    Book description: ${metadata.description}
    
    Write only the main body content without any section headings or titles.`,

  foreword: (metadata: BookMetadata) =>
    `Write a professional foreword for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    The foreword should:
    - Be written from the perspective of a respected expert in the field
    - Endorse the book's value and the author's expertise
    - Highlight the book's unique contributions
    - Explain why this book is needed now
    - Use a ${metadata.tone} tone
    - Be approximately 300-800 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    
    Write only the main body content without any section headings or titles.`,

  introduction: (metadata: BookMetadata, context?: BookContext) =>
    `Write a comprehensive introduction for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    The introduction should:
    - Hook the reader with an engaging opening
    - Clearly state the book's purpose and objectives
    - Outline what readers will learn or gain
    - Provide a roadmap of the book's structure
    - Establish credibility and context
    - Use a ${metadata.tone} tone throughout
    - Be approximately 1000-2000 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    ${context ? `Book outline: ${context.bookOutline}` : ''}
    
    Write only the main body content without any section headings or titles.`,

  chapter: (
    metadata: BookMetadata, 
    chapterOutline: ChapterOutline, 
    context?: BookContext,
    userContent?: string
  ) => {
    const contextSection = context && context.previousChapters.length > 0 
      ? `
    PREVIOUS CHAPTERS CONTEXT:
    ${context.previousChapters.map(ch => 
      `Chapter: ${ch.chapterId}
      Summary: ${ch.summary}
      Key Points: ${ch.keyPoints.join(', ')}`
    ).join('\n\n')}
    
    BOOK OUTLINE:
    ${context.bookOutline}
    ` 
      : context?.bookOutline ? `BOOK OUTLINE:\n${context.bookOutline}` : '';

    const userContentSection = userContent 
      ? `
    USER PROVIDED CONTENT:
    ${userContent}
    
    Please incorporate and expand upon this user content while maintaining narrative flow.`
      : '';

    return `Write Chapter ${chapterOutline.order}: "${chapterOutline.title}" for the book "${metadata.title}".
    
    Chapter Description: ${chapterOutline.description}
    Expected Word Count: ${chapterOutline.estimatedWordCount} words
    
    Chapter Structure:
    ${chapterOutline.subSections.map(sub => 
      `${sub.order}. ${sub.title} - ${sub.description}`
    ).join('\n')}
    
    ${contextSection}
    ${userContentSection}
    
    Requirements:
    - Write substantial, book-quality content (${chapterOutline.estimatedWordCount} words minimum)
    - Use hierarchical headings for sub-sections (## for main sections, ### for subsections)
    - Maintain consistency with previous chapters and overall book narrative
    - Include relevant examples, case studies, or illustrations where appropriate
    - Use a ${metadata.tone} tone throughout
    - Include proper citations where academic references would be appropriate
    - Ensure smooth transitions between sections
    - Target audience: ${metadata.targetAudience}
    
    Write only the main chapter content without the chapter title heading.`;
  },

  conclusion: (metadata: BookMetadata, context?: BookContext) => {
    const contextSection = context && context.previousChapters.length > 0
      ? `
    BOOK CONTENT SUMMARY:
    ${context.previousChapters.map(ch => 
      `Chapter: ${ch.chapterId}
      Key Points: ${ch.keyPoints.join(', ')}`
    ).join('\n\n')}
    
    Total Word Count: ${context.totalWordCount} words`
      : '';

    return `Write a compelling conclusion for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    ${contextSection}
    
    The conclusion should:
    - Synthesize the main themes and insights from all chapters
    - Reinforce the book's key messages and takeaways
    - Provide actionable next steps for readers
    - Address the book's original objectives stated in the introduction
    - End with a memorable and inspiring final thought
    - Use a ${metadata.tone} tone throughout
    - Be approximately 1000-2000 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    
    Write only the main body content without any section headings or titles.`;
  },

  appendix: (metadata: BookMetadata, appendixTitle: string, appendixDescription: string) =>
    `Write an appendix section titled "${appendixTitle}" for the book "${metadata.title}".
    
    Description: ${appendixDescription}
    
    The appendix should:
    - Provide supplementary material that supports the main content
    - Be well-organized and easy to reference
    - Include detailed information that would interrupt the flow if placed in main chapters
    - Use clear formatting and structure
    - Be approximately 500-1500 words
    
    Target audience: ${metadata.targetAudience}
    Tone: ${metadata.tone}
    
    Write only the main content without any section headings or titles.`,

  // Summary generation for context management
  chapterSummary: (chapterTitle: string, chapterContent: string) =>
    `Create a concise summary of the following chapter content for use in maintaining context for subsequent chapters.
    
    Chapter Title: ${chapterTitle}
    
    Chapter Content:
    ${chapterContent}
    
    Requirements:
    - Maximum 400 words
    - Focus on key concepts, main arguments, and important conclusions
    - Include 3-5 bullet points of the most important takeaways
    - Maintain the essential information needed for narrative continuity
    - Use clear, concise language
    
    Format your response as:
    Summary: [Your summary here]
    
    Key Points:
    • [Point 1]
    • [Point 2]
    • [Point 3]
    • [Point 4]
    • [Point 5]`,

  // Glossary generation
  glossary: (metadata: BookMetadata, allChapterContent: string) =>
    `Generate a comprehensive glossary for the book "${metadata.title}" based on the following content.
    
    Book Content:
    ${allChapterContent}
    
    Requirements:
    - Extract 15-30 key terms that readers might need defined
    - Provide clear, concise definitions (1-3 sentences each)
    - Focus on technical terms, jargon, or concepts specific to the book's subject
    - Arrange alphabetically
    - Use language appropriate for ${metadata.targetAudience}
    
    Format each entry as:
    **Term**: Definition here.
    
    Write only the glossary entries without any introductory text or headings.`,

  // Bibliography generation
  bibliography: (allCitations: string[]) =>
    `Generate a properly formatted bibliography based on the following citations found throughout the book:
    
    Citations:
    ${allCitations.join('\n')}
    
    Requirements:
    - Convert in-text citations to full bibliographic entries
    - Use consistent academic formatting (APA style preferred)
    - Arrange alphabetically by author surname
    - Include all necessary publication details
    - Ensure proper formatting for different source types (books, journals, websites, etc.)
    
    Write only the bibliography entries without any introductory text or headings.`
};

// Text placeholders for different content types
export const TEXT_PLACEHOLDERS = {
  chapter: "Describe the main concepts, arguments, and insights for this chapter. Include examples, case studies, or personal experiences that illustrate your points...",
  preface: "Share your personal motivation for writing this book and what readers can expect to gain...",
  introduction: "Provide an overview of the book's purpose, scope, and structure. Hook the reader with compelling opening thoughts...",
  conclusion: "Synthesize the main themes and provide actionable takeaways for readers...",
  appendix: "Include supplementary material, detailed examples, or reference information that supports your main content..."
};

// Figure placeholders for different content types
export const FIGURE_PLACEHOLDERS = {
  chapter: "Describe how this visual supports the chapter's main concepts or provides illustrative examples...",
  introduction: "Explain how this figure sets the context or introduces key concepts for the entire book...",
  conclusion: "Use this visual to summarize key insights or provide a memorable closing illustration..."
};
