import OpenAI from "openai";
import express from "express";
import cors from "cors";

const app = express();
app.use(cors());
app.use(express.json({ limit: "10mb" }));

const openai = new OpenAI({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.VITE_OPENROUTER_API_KEY,
  defaultHeaders: {
    "HTTP-Referer": "https://your-site.com", // Change to your deployed site
    "X-Title": "Paper Genius Platform",
  },
});

app.post("/ai", async (req, res) => {
  const { model, messages } = req.body;
  try {
    const completion = await openai.chat.completions.create({
      model,
      messages,
      max_tokens: 1024,
    });
    res.json(completion);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => console.log(`AI API running on http://localhost:${PORT}`));
