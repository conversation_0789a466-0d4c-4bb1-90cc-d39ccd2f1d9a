import { AIGenerationOptions, AIAnalysisResult, BookContext, ChapterContext, GeneratedOutline, BookMetadata, UserChapter } from '../types';
import { CONTEXT_SETTINGS } from '../constants';
import { OUTLINE_GENERATION_PROMPTS } from '../prompts';

/**
 * Service for handling AI book generation with context management
 * Extends the paper AI service with book-specific capabilities
 */
export class BookAIService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }
  
  /**
   * Helper method to extract and parse JSON from AI responses
   * Handles malformed JSON with aggressive repair strategies
   */
  private extractAndParseJSON(content: string): any {
    console.log("Attempting to parse AI response:", content.substring(0, 100) + "...");

    // Extract JSON content from various formats
    let jsonContent = this.extractJSONContent(content);

    // Apply aggressive malformed JSON repair
    jsonContent = this.repairMalformedJSON(jsonContent);

    console.log("Repaired JSON (first 200 chars):", jsonContent.substring(0, 200));

    // Try parsing the repaired JSON
    try {
      return JSON.parse(jsonContent);
    } catch (error) {
      console.log("Primary JSON parse failed, attempting advanced recovery strategies...");

      // Advanced recovery strategies
      return this.attemptJSONRecovery(jsonContent, error as Error);
    }
  }

  /**
   * Extract JSON content from various AI response formats
   */
  private extractJSONContent(content: string): string {
    let jsonContent = content.trim();

    // First try to parse directly
    try {
      JSON.parse(jsonContent);
      return jsonContent; // If it parses, return as-is
    } catch (e) {
      // Continue with extraction
    }

    // Match content inside code blocks with or without json specifier
    const codeBlockRegexes = [
      /```(?:json)?\s*([\s\S]*?)\s*```/,     // Standard markdown code blocks
      /`{3,}(?:json)?\s*([\s\S]*?)\s*`{3,}/, // Handle cases with more than 3 backticks
      /\{[\s\S]*\}/                          // Just try to find a JSON object directly
    ];

    for (const regex of codeBlockRegexes) {
      const match = content.match(regex);
      if (match && match[1]) {
        jsonContent = match[1].trim();
        console.log("Extracted content from code block");
        break;
      }
    }

    // If we couldn't extract from a code block, look for first { and last }
    if (jsonContent === content.trim()) {
      console.log("No code block match - looking for JSON directly");
      const firstBrace = content.indexOf('{');
      const lastBrace = content.lastIndexOf('}');
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = content.substring(firstBrace, lastBrace + 1);
        console.log("Extracted content using brace indices");
      }
    }

    return jsonContent;
  }

  /**
   * Aggressively repair malformed JSON from AI responses
   */
  private repairMalformedJSON(jsonContent: string): string {
    console.log("Applying aggressive JSON repair...");

    // Step 1: Remove all control characters that break JSON parsing
    let repaired = jsonContent.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

    // Step 2: Fix the most common malformed pattern: escaped quotes in property names
    // "chapters\": → "chapters":
    repaired = repaired.replace(/\\":/g, '":');

    // Step 3: Fix escaped quotes in property values
    // : "value\" → : "value"
    repaired = repaired.replace(/:\s*"([^"]*)\\"(\s*[,}\]])/g, ': "$1"$2');

    // Step 4: Fix escaped quotes at the start of property names
    // \"property": → "property":
    repaired = repaired.replace(/\\"([^"]*)":/g, '"$1":');

    // Step 5: Fix escaped quotes in array elements
    // ["item\", → ["item",
    repaired = repaired.replace(/\\"(\s*[,\]])/g, '"$1');

    // Step 6: Remove any remaining unnecessary escaping
    repaired = repaired.replace(/\\"/g, '"');

    // Step 7: Fix double escapes
    repaired = repaired.replace(/\\\\/g, '\\');

    // Step 8: Replace smart quotes and normalize
    repaired = repaired
      .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes
      .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
      .replace(/\n+/g, '\n')           // Normalize newlines
      .trim();

    console.log("JSON repair completed");
    return repaired;
  }

  /**
   * Attempt various JSON recovery strategies when parsing fails
   */
  private attemptJSONRecovery(jsonContent: string, originalError: Error): any {
    console.log("Attempting JSON recovery strategies...");

    // Strategy 1: Fix trailing commas and basic formatting
    try {
      let fixedJson = jsonContent;

      // Fix trailing commas in arrays/objects
      fixedJson = fixedJson.replace(/,(\s*[\]}])/g, '$1');

      // Fix missing quotes around property names
      fixedJson = fixedJson.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');

      // Fix single quotes used instead of double quotes
      fixedJson = fixedJson.replace(/'([^']*)'/g, '"$1"');

      console.log("Attempting parse with basic formatting fixes");
      return JSON.parse(fixedJson);
    } catch (basicError) {
      console.log("Basic formatting fixes failed, trying bracket completion...");
    }

    // Strategy 2: Fix incomplete JSON by adding missing closing brackets
    try {
      let repairedJson = jsonContent;
      const openBraces = (repairedJson.match(/\{/g) || []).length;
      const closeBraces = (repairedJson.match(/\}/g) || []).length;
      const openBrackets = (repairedJson.match(/\[/g) || []).length;
      const closeBrackets = (repairedJson.match(/\]/g) || []).length;

      // Add missing closing braces
      for (let i = 0; i < openBraces - closeBraces; i++) {
        repairedJson += '}';
      }

      // Add missing closing brackets
      for (let i = 0; i < openBrackets - closeBrackets; i++) {
        repairedJson += ']';
      }

      console.log("Attempting parse with bracket completion");
      return JSON.parse(repairedJson);
    } catch (bracketError) {
      console.log("Bracket completion failed, trying regex extraction...");
    }

    // Strategy 3: Extract chapters array using regex
    try {
      console.log("Attempting regex extraction of chapters array");
      const chaptersMatch = jsonContent.match(/"chapters"\s*:\s*(\[[\s\S]*?\])/);
      if (chaptersMatch && chaptersMatch[1]) {
        const chaptersJson = `{"chapters":${chaptersMatch[1]}}`;
        return JSON.parse(chaptersJson);
      }

      // Try to extract individual chapter objects and reconstruct
      const chapterMatches = jsonContent.match(/\{[^{}]*"title"[^{}]*\}/g);
      if (chapterMatches && chapterMatches.length > 0) {
        const chapters = chapterMatches.map(match => {
          try {
            return JSON.parse(match);
          } catch {
            return null;
          }
        }).filter(Boolean);

        if (chapters.length > 0) {
          console.log(`Reconstructed ${chapters.length} chapters from individual matches`);
          return { chapters };
        }
      }
    } catch (regexError) {
      console.log("Regex extraction failed");
    }

    // All strategies failed
    console.error('All JSON recovery strategies failed');
    console.error('Original error:', originalError.message);
    console.error('Content that failed to parse (first 500 chars):', jsonContent.substring(0, 500));
    throw new Error(`Failed to parse JSON: ${originalError.message || 'Invalid format'}`);
  }

  /**
   * Generate a book chapter with context awareness
   */
  async generateChapter(
    chapterPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { 
        model = "anthropic/claude-3.5-sonnet", 
        maxTokens = CONTEXT_SETTINGS.CHAPTER_GENERATION_TOKENS,
        context
      } = options;
      
      // Build context-aware system prompt
      const systemPrompt = this.buildSystemPrompt(context);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: chapterPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7 // Slightly creative for book writing
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No content generated]';
    } catch (error: any) {
      console.error('AI Chapter Generation Error:', error);
      throw new Error('Failed to generate chapter. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate comprehensive outlines for all chapters
   */
  async generateAllChapterOutlines(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    try {
      // For large numbers of chapters (6+), use chunking strategy
      if (userChapters.length >= 6) {
        return this.generateChapterOutlinesInChunks(metadata, userChapters, options);
      }

      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 6144 // Increased token limit for better outline generation
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(metadata, userChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: `You are an expert book outline generator. Create comprehensive, detailed outlines that provide clear structure for book chapters.

CRITICAL JSON FORMATTING RULES:
- You must respond with raw valid JSON only
- Do not wrap your response in code blocks, markdown, or any other formatting
- Do not use escaped quotes (\") in property names or values
- Use only standard double quotes (") for all JSON strings
- Do not include any control characters or special formatting
- Ensure all property names and string values are properly quoted
- Do not escape quotes unless absolutely necessary for content within strings
- Keep all content simple and JSON-safe
- Check that your JSON is valid before responding

The entire response should be a single valid JSON object that can be directly parsed.`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for outline generation (first 200 chars):", 
          content.length > 200 ? content.substring(0, 200) + "..." : content);
        
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content);
        
        // Validate the parsed JSON structure
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result');
          throw new Error('Failed to parse AI response as JSON. The AI returned an invalid response format.');
        }
        
        if (!parsed.chapters || !Array.isArray(parsed.chapters)) {
          console.error('Invalid outline format, missing chapters array:', parsed);
          throw new Error('Invalid outline format returned by AI. Missing chapters array.');
        }

        const outlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => {
          // Validate chapter structure and provide defaults
          if (!chapter.title || !chapter.sections || !Array.isArray(chapter.sections)) {
            console.error('Invalid chapter structure:', chapter);
            throw new Error(`Invalid structure for chapter ${index + 1}. Missing title or sections.`);
          }

          return {
            id: `outline-${userChapters[index]?.id || index}`,
            chapterId: userChapters[index]?.id || `chapter-${index}`,
            title: chapter.title || `Chapter ${index + 1}`,
            description: chapter.description || '',
            sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
              id: `section-${section.order || sectionIndex + 1}`,
              title: section.title || `Section ${sectionIndex + 1}`,
              description: section.description || '',
              level: section.level || 1,
              order: section.order || sectionIndex + 1,
              estimatedWordCount: section.estimatedWordCount || 500,
              keyPoints: section.keyPoints || []
            })),
            estimatedWordCount: chapter.estimatedWordCount || 3000,
            keyPoints: chapter.keyPoints || [],
            status: 'ready' as const
          };
        });

        return outlines;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outlines. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outlines. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate chapter outlines in chunks to handle large numbers of chapters
   */
  private async generateChapterOutlinesInChunks(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    const chunkSize = 4; // Process 4 chapters at a time
    const allOutlines: GeneratedOutline[] = [];

    console.log(`Generating outlines for ${userChapters.length} chapters in chunks of ${chunkSize}`);

    for (let i = 0; i < userChapters.length; i += chunkSize) {
      const chunk = userChapters.slice(i, i + chunkSize);
      const chunkNumber = Math.floor(i / chunkSize) + 1;
      const totalChunks = Math.ceil(userChapters.length / chunkSize);

      console.log(`Processing chunk ${chunkNumber}/${totalChunks} (chapters ${i + 1}-${Math.min(i + chunkSize, userChapters.length)})`);

      try {
        const {
          model = "anthropic/claude-3.5-sonnet",
          maxTokens = 4096
        } = options;

        const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(metadata, chunk);

        const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model,
            messages: [
              {
                role: "system",
                content: `You are an expert book editor and outline creator. Generate detailed, comprehensive chapter outlines that provide clear structure for book writing.

CRITICAL JSON FORMATTING RULES:
- You must respond with raw valid JSON only
- Do not wrap your response in code blocks, markdown, or any other formatting
- Do not use escaped quotes (\") in property names or values
- Use only standard double quotes (") for all JSON strings
- Do not include any control characters or special formatting
- Ensure all property names and string values are properly quoted
- Do not escape quotes unless absolutely necessary for content within strings
- Keep all content simple and JSON-safe

The entire response should be a single valid JSON object that can be directly parsed.`
              },
              {
                role: "user",
                content: prompt
              }
            ],
            max_tokens: maxTokens,
            temperature: 0.7
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        const content = data.choices?.[0]?.message?.content || '';

        console.log(`AI Response for chunk ${chunkNumber} (first 200 chars):`,
          content.length > 200 ? content.substring(0, 200) + "..." : content);

        // Parse the JSON response with enhanced error handling
        const parsed = this.extractAndParseJSON(content);

        if (!parsed || !parsed.chapters || !Array.isArray(parsed.chapters)) {
          console.error(`Invalid response structure for chunk ${chunkNumber}`);
          throw new Error(`Invalid outline structure in chunk ${chunkNumber}`);
        }

        const chunkOutlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => {
          const originalIndex = i + index;
          return {
            id: `outline-${chunk[index]?.id || originalIndex}`,
            chapterId: chunk[index]?.id || `chapter-${originalIndex}`,
            title: chapter.title || `Chapter ${originalIndex + 1}`,
            description: chapter.description || '',
            sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
              id: `section-${section.order || sectionIndex + 1}`,
              title: section.title || `Section ${sectionIndex + 1}`,
              description: section.description || '',
              level: section.level || 1,
              order: section.order || sectionIndex + 1,
              estimatedWordCount: section.estimatedWordCount || 500,
              keyPoints: section.keyPoints || []
            })),
            estimatedWordCount: chapter.estimatedWordCount || 3000,
            keyPoints: chapter.keyPoints || [],
            status: 'ready' as const
          };
        });

        allOutlines.push(...chunkOutlines);
        console.log(`Successfully processed chunk ${chunkNumber}, total outlines: ${allOutlines.length}`);

        // Add a small delay between chunks to avoid rate limiting
        if (i + chunkSize < userChapters.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (chunkError: any) {
        console.error(`Error processing chunk ${chunkNumber}:`, chunkError);

        // Create fallback outlines for this chunk
        const fallbackOutlines = chunk.map((userChapter) => ({
          id: `outline-${userChapter.id}`,
          chapterId: userChapter.id,
          title: userChapter.outline.title,
          description: userChapter.outline.description || `Outline for ${userChapter.outline.title}`,
          sections: [{
            id: 'section-1',
            title: 'Main Content',
            description: 'Chapter content based on user input',
            level: 1,
            order: 1,
            estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
            keyPoints: []
          }],
          estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
          keyPoints: [],
          status: 'ready' as const
        }));

        allOutlines.push(...fallbackOutlines);
        console.log(`Added fallback outlines for chunk ${chunkNumber}`);
      }
    }

    console.log(`Completed outline generation for all ${userChapters.length} chapters`);
    return allOutlines;
  }

  /**
   * Generate outline for a single chapter
   */
  async generateChapterOutline(
    metadata: BookMetadata,
    userChapter: UserChapter,
    previousChapters: string[] = [],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline> {
    try {
      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 2048
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateChapterOutline(metadata, userChapter, previousChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book outline generator. Create detailed, comprehensive outlines for book chapters. CRITICAL REQUIREMENTS: 1) You MUST respond with raw valid JSON only, 2) Do NOT use markdown code blocks or any other formatting, 3) Do NOT include ANY explanation before or after the JSON, 4) The ENTIRE response should be a single valid JSON object, 5) Use ONLY double quotes for all properties and strings, 6) NEVER use single quotes, 7) Check that your JSON is valid before responding. FAILURE TO FOLLOW THESE REQUIREMENTS WILL BREAK THE SYSTEM."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      // Parse the JSON response
      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for chapter outline generation (first 200 chars):", 
          content.length > 200 ? content.substring(0, 200) + "..." : content);
        
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content);
        
        // Validate the parsed result
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result');
          throw new Error('Failed to parse AI response as JSON. The AI returned an invalid response format.');
        }
        
        console.log("Successfully parsed chapter outline JSON");

        // Validate and build outline with defaults for missing fields
        const outline: GeneratedOutline = {
          id: `outline-${userChapter.id}`,
          chapterId: userChapter.id,
          title: parsed.title || userChapter.outline.title,
          description: parsed.description || userChapter.outline.description,
          sections: (parsed.sections || []).map((section: any, index: number) => ({
            id: `section-${section.order || index + 1}`,
            title: section.title || `Section ${index + 1}`,
            description: section.description || '',
            level: section.level || 1,
            order: section.order || index + 1,
            estimatedWordCount: section.estimatedWordCount || 500,
            keyPoints: section.keyPoints || []
          })),
          estimatedWordCount: parsed.estimatedWordCount || userChapter.outline.estimatedWordCount || 3000,
          keyPoints: parsed.keyPoints || [],
          status: 'ready' as const
        };

        return outline;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outline. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Chapter Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outline. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate a summary of a chapter for context building
   */
  async generateChapterSummary(
    chapterTitle: string,
    chapterContent: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<AIAnalysisResult> {
    try {
      const {
        model = "anthropic/claude-3-sonnet",
        maxTokens = CONTEXT_SETTINGS.SUMMARY_GENERATION_TOKENS
      } = options;

      // Truncate content intelligently to avoid token limits while preserving key information
      const truncatedContent = this.intelligentContentTruncation(chapterContent, 8000);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: `You are an expert at creating concise chapter summaries for book context management.

              Create a summary that:
              - Is EXACTLY ${CONTEXT_SETTINGS.SUMMARY_MAX_WORDS} words or fewer
              - Captures the key narrative developments, character arcs, and plot points
              - Maintains continuity for subsequent chapters
              - Focuses on actionable information that affects future chapters
              - Includes important world-building or setting details if relevant

              IMPORTANT JSON FORMATTING RULES:
              - Avoid mathematical expressions with backslashes (use plain text descriptions instead)
              - Do not use quotes within quoted strings (use single quotes or rephrase)
              - Keep all content simple and JSON-safe
              - If you must reference equations, describe them in words rather than symbols

              Return your response as JSON in this exact format:
              {
                "summary": "Your concise summary here",
                "keyPoints": ["Key point 1", "Key point 2", "Key point 3"]
              }

              THE ENTIRE RESPONSE MUST BE VALID JSON WITH NO OTHER TEXT.`
            },
            {
              role: "user",
              content: `Create a concise summary for the chapter "${chapterTitle}":\n\n${truncatedContent}`
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.3 // Lower temperature for consistent summaries
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for chapter summary (first 200 chars):",
          content.length > 200 ? content.substring(0, 200) + "..." : content);

        // Use the helper method to parse and extract JSON with additional fallback
        let parsed: any;
        try {
          parsed = this.extractAndParseJSON(content);
        } catch (parseError) {
          console.warn('Primary JSON parsing failed, attempting manual cleanup for chapter summary');

          // Manual cleanup specifically for chapter summaries
          try {
            let cleanContent = content.trim();

            // Remove any non-JSON text before and after
            const jsonStart = cleanContent.indexOf('{');
            const jsonEnd = cleanContent.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1) {
              cleanContent = cleanContent.substring(jsonStart, jsonEnd + 1);
            }

            // Replace problematic LaTeX expressions with plain text
            cleanContent = cleanContent.replace(/\$\\Delta\\phi[^$]*\$/g, 'phase difference equation');
            cleanContent = cleanContent.replace(/\$\\frac\{[^}]*\}\{[^}]*\}[^$]*\$/g, 'mathematical formula');
            cleanContent = cleanContent.replace(/\$[^$]*\$/g, 'equation');

            // Fix unescaped quotes by replacing them with single quotes
            cleanContent = cleanContent.replace(/"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}\]])/g, '"$1\'$2\'$3"');

            // Try parsing the cleaned content
            parsed = JSON.parse(cleanContent);
            console.log('Manual cleanup successful for chapter summary');
          } catch (manualError) {
            console.error('Manual cleanup also failed:', manualError);
            return this.createFallbackSummaryResult(chapterTitle, chapterContent);
          }
        }

        // Validate the parsed result
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result for chapter summary');
          return this.createFallbackSummaryResult(chapterTitle, chapterContent);
        }

        // Validate required fields and word count
        const summary = parsed.summary || `Summary of "${chapterTitle}" (missing summary)`;
        const keyPoints = Array.isArray(parsed.keyPoints) ? parsed.keyPoints : [];

        // Enforce word count limit strictly
        const validatedSummary = this.enforceWordLimit(summary, CONTEXT_SETTINGS.SUMMARY_MAX_WORDS);

        console.log(`Generated summary for "${chapterTitle}" (${summary.split(/\s+/).length} words, final: ${validatedSummary.split(/\s+/).length} words)`);

        return {
          summary: validatedSummary,
          keyPoints: keyPoints.slice(0, 5) // Limit key points to 5 max
        };
      } catch (parseError: any) {
        console.error('Failed to parse chapter summary JSON:', parseError);
        return this.createFallbackSummaryResult(chapterTitle, chapterContent);
      }
    } catch (error: any) {
      console.error('AI Chapter Summary Generation Error:', error);
      return this.createFallbackSummaryResult(chapterTitle, chapterContent);
    }
  }

  /**
   * Intelligent content truncation that preserves key information
   */
  private intelligentContentTruncation(content: string, maxChars: number): string {
    if (content.length <= maxChars) {
      return content;
    }

    // Try to find natural break points (paragraphs, sentences)
    const paragraphs = content.split('\n\n');
    let truncated = '';

    for (const paragraph of paragraphs) {
      if ((truncated + paragraph).length > maxChars) {
        // If adding this paragraph would exceed limit, try to add part of it
        const remainingChars = maxChars - truncated.length;
        if (remainingChars > 100) { // Only add if we have meaningful space left
          const sentences = paragraph.split('. ');
          for (const sentence of sentences) {
            if ((truncated + sentence + '. ').length <= maxChars) {
              truncated += sentence + '. ';
            } else {
              break;
            }
          }
        }
        break;
      }
      truncated += paragraph + '\n\n';
    }

    return truncated.trim() || content.slice(0, maxChars);
  }

  /**
   * Enforce word limit on summary text
   */
  private enforceWordLimit(text: string, maxWords: number): string {
    const words = text.split(/\s+/);
    if (words.length <= maxWords) {
      return text;
    }

    return words.slice(0, maxWords).join(' ') + '...';
  }

  /**
   * Create a basic fallback summary from chapter content
   */
  private createFallbackSummary(chapterTitle: string, chapterContent: string): string {
    // Extract first few sentences as a basic summary
    const sentences = chapterContent.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const firstSentences = sentences.slice(0, 3).join('. ').trim();

    if (firstSentences.length > 0) {
      return `Chapter "${chapterTitle}": ${firstSentences}${firstSentences.endsWith('.') ? '' : '.'}`;
    }

    // If no good sentences found, create a basic summary
    const wordCount = chapterContent.split(/\s+/).length;
    return `Chapter "${chapterTitle}" contains ${wordCount} words of content covering the chapter's main topics and developments.`;
  }

  /**
   * Create fallback summary result when AI generation fails
   */
  private createFallbackSummaryResult(chapterTitle: string, chapterContent: string): AIAnalysisResult {
    const fallbackSummary = this.createFallbackSummary(chapterTitle, chapterContent);
    return {
      summary: this.enforceWordLimit(fallbackSummary, CONTEXT_SETTINGS.SUMMARY_MAX_WORDS),
      keyPoints: [`Chapter: ${chapterTitle}`, 'Summary generated from content analysis']
    };
  }

  /**
   * Build a system prompt with book context for consistent generation
   */
  private buildSystemPrompt(context?: BookContext): string {
    const basePrompt = `You are an expert book author with deep knowledge across many subjects.
Write in a clear, engaging, and professional style appropriate for a published book.
Create substantial, high-quality content that incorporates creative elements, examples, and depth.
Maintain consistency with the book's voice, tone, and previous chapters.

IMPORTANT: Your task is to generate a complete chapter for the book. Follow the outline structure carefully and create high-quality, comprehensive content that builds upon any previous chapters provided in the context.`;
    
    if (!context) {
      return basePrompt;
    }
    
    const contextPrompt = `
You have access to the following context from the book:

BOOK OUTLINE:
${context.bookOutline}

${context.previousChapters.length > 0 ? `PREVIOUS CHAPTERS:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
   Summary: ${ch.summary}
   Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}` : 'NOTE: This is the first chapter of the book.'}

CRITICAL INSTRUCTIONS:
1. Follow the chapter outline structure exactly
2. Build on previous chapters' content for narrative continuity
3. Maintain the book's voice and tone consistently
4. Create substantive, high-quality content that meets or exceeds the word count target
5. Include proper transitions that reference previous chapters where appropriate
6. Ensure your writing maintains consistent tone, narrative, and references throughout the book`;
    
    return basePrompt + contextPrompt;
  }
}

// Export as singleton
const bookAIService = new BookAIService();
export default bookAIService;
