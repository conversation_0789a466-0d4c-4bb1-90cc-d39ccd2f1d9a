import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  BookMetadata, 
  UserInputs, 
  UserChapter, 
  GeneratedChapter, 
  ChapterOutline,
  ContentItem,
  Citation
} from './types';
import { AI_MODELS, BOOK_SECTION_TYPES } from './constants';
import { BOOK_SECTION_PROMPTS } from './prompts';
import { BookMetadataForm } from './components/BookMetadataForm';
import { ChapterCard } from './components/ChapterCard';
import { BookGenerationPanel } from './components/BookGenerationPanel';
import { BookGenerationWorkflow } from './components/BookGenerationWorkflow';
import { AIModelSelector } from './components/AIModelSelector';
import { useBookContextStore } from './stores/book-context.store';
import bookAIService from './services/book-ai.service';
import { extractCitationsFromText } from '../paper-generator/citation-extraction.enhanced';
import { editorService } from '../paper-generator/editor.service';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, BookOpen, ArrowLeft, ArrowRight } from "lucide-react";

export function AIBookGenerator() {
  // State for user inputs
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      subtitle: "",
      genre: "",
      targetAudience: "",
      keywords: [],
      authors: [],
      description: "",
      estimatedLength: "medium",
      tone: "professional"
    },
    userChapters: []
  });

  // Track generated chapters and sections
  const [generatedChapters, setGeneratedChapters] = useState<GeneratedChapter[]>([]);
  const [generatedSections, setGeneratedSections] = useState<GeneratedChapter[]>([]);

  // UI state
  const [currentStep, setCurrentStep] = useState<'metadata' | 'chapters' | 'generation' | 'completed'>('metadata');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);

  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [chapterCitations, setChapterCitations] = useState<Record<string, string[]>>({});

  // Context store
  const {
    initializeBookContext,
    setGenerationQueue,
    startChapterGeneration,
    completeChapterGeneration,
    updateGenerationProgress,
    getContextForChapter,
    resetContext
  } = useBookContextStore();

  // Initialize generated sections based on book structure
  useEffect(() => {
    const sections = BOOK_SECTION_TYPES
      .filter(type => !type.isChapter)
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        title: type.name,
        description: type.description,
        status: 'pending' as const,
        icon: type.icon,
        order: type.order
      }));

    setGeneratedSections(sections);
  }, []);

  const handleMetadataChange = (metadata: BookMetadata) => {
    setUserInputs(prev => ({ ...prev, metadata }));
  };

  const handleNextFromMetadata = () => {
    setCurrentStep('chapters');
  };

  const addChapter = (count: number = 1) => {
    const currentCount = userInputs.userChapters.length;
    const newChapters: UserChapter[] = [];
    
    for (let i = 0; i < count; i++) {
      const chapterNumber = currentCount + i + 1;
      newChapters.push({
        id: `chapter-${Date.now()}-${i}`,
        title: `Chapter ${chapterNumber}`,
        outline: {
          id: `outline-${Date.now()}-${i}`,
          title: `Chapter ${chapterNumber}`,
          description: '',
          subSections: [],
          estimatedWordCount: 3000,
          order: chapterNumber
        },
        items: []
      });
    }

    setUserInputs(prev => ({
      ...prev,
      userChapters: [...prev.userChapters, ...newChapters]
    }));
  };

  const updateChapter = (chapterId: string, updates: Partial<UserChapter>) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  const removeChapter = (chapterId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.filter(chapter => chapter.id !== chapterId)
    }));
  };

  const moveChapter = (chapterId: string, direction: 'up' | 'down') => {
    const chapters = [...userInputs.userChapters];
    const index = chapters.findIndex(ch => ch.id === chapterId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= chapters.length) return;
    
    [chapters[index], chapters[newIndex]] = [chapters[newIndex], chapters[index]];
    
    // Update order values
    chapters.forEach((chapter, idx) => {
      chapter.outline.order = idx + 1;
    });
    
    setUserInputs(prev => ({ ...prev, userChapters: chapters }));
  };

  const handleStartGeneration = () => {
    if (userInputs.userChapters.length === 0) {
      toast.error("Please add at least one chapter before generating");
      return;
    }

    setCurrentStep('generation');
  };

  const handleGenerationComplete = (chapters: GeneratedChapter[], citations: Citation[]) => {
    setGeneratedChapters(chapters);
    setAllCitations(citations);
    setCurrentStep('completed');
    toast.success("Book generation completed successfully!");
  };





  const canProceedToGeneration = () => {
    return userInputs.metadata.title.trim() !== '' &&
           userInputs.metadata.genre !== '' &&
           userInputs.metadata.targetAudience !== '' &&
           userInputs.userChapters.length > 0;
  };

  const handleEditInEditor = () => {
    // Generate complete book content
    const allContent = [...generatedSections, ...generatedChapters]
      .filter(item => item.content)
      .sort((a, b) => a.order - b.order);

    let bookContent = '';

    // Title page
    bookContent += `# ${userInputs.metadata.title}\n\n`;
    if (userInputs.metadata.subtitle) {
      bookContent += `## ${userInputs.metadata.subtitle}\n\n`;
    }
    if (userInputs.metadata.authors.length > 0) {
      bookContent += `**Authors: <AUTHORS>
    }
    bookContent += `---\n\n`;

    // Main content
    allContent.forEach((item, index) => {
      bookContent += `# ${item.title}\n\n`;
      bookContent += `${item.content}\n\n`;

      if (item.wordCount) {
        bookContent += `*Word count: ${item.wordCount.toLocaleString()}*\n\n`;
      }

      bookContent += `---\n\n`;
    });

    // Send to editor
    editorService.sendToMainEditor({
      title: userInputs.metadata.title,
      content: bookContent
    });

    toast.success("Book loaded in editor for further editing.");
  };

  // Render based on current step
  if (currentStep === 'metadata') {
    return (
      <BookMetadataForm
        metadata={userInputs.metadata}
        onMetadataChange={handleMetadataChange}
        onNext={handleNextFromMetadata}
      />
    );
  }

  if (currentStep === 'chapters') {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
            <BookOpen className="h-10 w-10 text-blue-600" />
            Define Your Chapters
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create detailed chapter outlines and provide any content you want the AI to incorporate.
          </p>
        </div>

        {/* AI Model Selector */}
        <AIModelSelector
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          models={AI_MODELS}
        />

        {/* Chapter Cards */}
        <div className="space-y-6">
          {userInputs.userChapters.map((chapter, index) => (
            <ChapterCard
              key={chapter.id}
              chapter={chapter}
              chapterIndex={index}
              onChapterUpdate={updateChapter}
              onChapterRemove={removeChapter}
              onMoveChapter={moveChapter}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
            />
          ))}

          {/* Add Chapter Buttons */}
          <div className="flex flex-col md:flex-row justify-center items-center gap-4">
            <div className="w-full md:w-auto flex flex-col gap-2 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-medium mb-2 text-center">Add Chapters</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button onClick={() => addChapter(1)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  1 Chapter
                </Button>
                
                <Button onClick={() => addChapter(3)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  3 Chapters
                </Button>
                
                <Button onClick={() => addChapter(5)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  5 Chapters
                </Button>
              </div>
              
              <div className="mt-2">
                <Button 
                  onClick={() => {
                    const count = window.prompt("How many chapters do you want to add?", "1");
                    const numChapters = parseInt(count || "1", 10);
                    if (!isNaN(numChapters) && numChapters > 0 && numChapters <= 30) {
                      addChapter(numChapters);
                    } else if (numChapters > 30) {
                      toast.warning("Maximum 30 chapters allowed at once");
                    }
                  }} 
                  size="sm" 
                  variant="default"
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Custom Number of Chapters
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button 
            onClick={() => setCurrentStep('metadata')}
            variant="outline"
            size="lg"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Metadata
          </Button>
          
          <Button 
            onClick={handleStartGeneration}
            disabled={!canProceedToGeneration()}
            size="lg"
            className="px-8"
          >
            Generate Book
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    );
  }

  if (currentStep === 'generation') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <BookGenerationWorkflow
            bookMetadata={userInputs.metadata}
            userChapters={userInputs.userChapters}
            selectedModel={selectedModel}
            onComplete={handleGenerationComplete}
            onBack={() => setCurrentStep('chapters')}
          />
        </div>
      </div>
    );
  }

  if (currentStep === 'completed') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Book Generation Complete!
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Your book "{userInputs.metadata.title}" has been successfully generated with context-aware AI assistance.
            </p>
          </div>

          <BookGenerationPanel
            generatedChapters={generatedChapters}
            generatedSections={generatedSections}
            isGenerating={false}
            bookMetadata={userInputs.metadata}
            allCitations={allCitations}
            chapterCitations={chapterCitations}
            onEditInEditor={handleEditInEditor}
          />
        </div>
      </div>
    );
  }

  return null;
}
