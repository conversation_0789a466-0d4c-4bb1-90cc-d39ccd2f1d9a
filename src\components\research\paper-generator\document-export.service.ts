import { GeneratedSection } from './types';
import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';

// Helper function to create HTML content for the paper (for existing functionality)
const createPaperHTML = (title: string, authors: string[], generatedSections: GeneratedSection[]): string => {
  const authorsText = authors.length > 0 
    ? authors.join(', ')
    : 'Anonymous';

  const sectionsHTML = generatedSections
    .filter(section => section.status === 'completed' && section.content)
    .sort((a, b) => {
      // Order should be: Abstract, Introduction, Literature Review, Methodology, Results, Discussion, Conclusion
      const sectionOrder: Record<string, number> = {
        'abstract': 1,
        'introduction': 2,
        'literature-review': 3,
        'methodology': 4,
        'results': 5,
        'discussion': 6,
        'conclusion': 7
      };
      return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
    })
    .map(section => `
      <div class="section">
        <h2>${section.name}</h2>
        <div>${section.content?.replace(/\n/g, '<br/>')}</div>
      </div>
    `)
    .join('');

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body {
          font-family: 'Times New Roman', Times, serif;
          line-height: 1.6;
          margin: 0;
          padding: 20px;
          max-width: 800px;
          margin: 0 auto;
        }
        h1 {
          text-align: center;
          font-size: 24px;
          margin-bottom: 10px;
        }
        .authors {
          text-align: center;
          font-style: italic;
          margin-bottom: 20px;
          font-size: 16px;
        }
        h2 {
          font-size: 18px;
          margin-top: 20px;
          margin-bottom: 10px;
        }
        .section {
          margin-bottom: 20px;
        }
      </style>
    </head>
    <body>
      <h1>${title}</h1>
      <div class="authors">${authorsText}</div>
      ${sectionsHTML}
    </body>
    </html>
  `;
};

// Legacy export functions (preserved for backward compatibility)
const exportToWord = (title: string, authors: string[], generatedSections: GeneratedSection[]): void => {
  const htmlContent = createPaperHTML(title, authors, generatedSections);
  
  // Create a Blob with the HTML content
  const blob = new Blob([htmlContent], { type: 'application/msword' });
  
  // Create a download link and trigger the download
  const a = document.createElement('a');
  const url = URL.createObjectURL(blob);
  a.href = url;
  a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}.doc`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Legacy PDF export (preserved for backward compatibility)
const exportToPDF = (title: string, authors: string[], generatedSections: GeneratedSection[]): void => {
  const htmlContent = createPaperHTML(title, authors, generatedSections);
  
  // Create a new window and write the HTML content to it
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    
    // Give the browser a moment to load the content before printing
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  }
};

/**
 * Convert markdown to docx document
 * 
 * @param title Document title
 * @param content Markdown content
 * @returns Document object
 */
const markdownToDocx = (title: string, content: string): Document => {
  // Parse content to identify headings, paragraphs, etc.
  const lines = content.split('\n');
  const documentChildren = [];
  
  // Add title
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 36,
        }),
      ],
      spacing: { after: 300 },
    })
  );
  
  // Process content line by line
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Skip empty lines
    if (!line) {
      documentChildren.push(new Paragraph({}));
      continue;
    }
    
    // Handle headings
    if (line.startsWith('# ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 240, after: 120 }
        })
      );
    } else if (line.startsWith('## ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(3),
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 240, after: 120 }
        })
      );
    } else if (line.startsWith('### ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(4),
          heading: HeadingLevel.HEADING_3,
          spacing: { before: 240, after: 120 }
        })
      );
    }
    
    // Handle lists - simple implementation
    else if (line.startsWith('- ') || line.startsWith('* ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          bullet: { level: 0 }
        })
      );
    }
    
    // Handle numbered lists
    else if (/^\d+\.\s/.test(line)) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(line.indexOf('.') + 1).trim(),
          numbering: {
            reference: "1",
            level: 0
          }
        })
      );
    }
    
    // Handle blockquotes
    else if (line.startsWith('> ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          indent: { left: 400 },
          style: 'Quote'
        })
      );
    }
    
    // Regular paragraph (check for inline formatting)
    else {
      let text = line;
      
      // Strip markdown formatting for now
      // For a production app, would need more sophisticated parsing
      text = text.replace(/\*\*(.*?)\*\*/g, '$1');
      text = text.replace(/\*(.*?)\*/g, '$1');
      
      documentChildren.push(
        new Paragraph({
          text: text
        })
      );
    }
  }
  
  return new Document({
    sections: [
      {
        properties: {},
        children: documentChildren,
      },
    ],
  });
};

/**
 * Generate and export a DOCX document from markdown content
 * 
 * @param title Document title
 * @param content Markdown content
 * @param fileName Output file name
 */
const exportMarkdownToDocx = async (title: string, content: string, fileName: string): Promise<void> => {
  try {
    // Make sure filename has .docx extension
    if (!fileName.toLowerCase().endsWith('.docx')) {
      fileName = `${fileName}.docx`;
    }
    
    const doc = markdownToDocx(title, content);
    
    // Generate DOCX blob
    const blob = await Packer.toBlob(doc);
    saveAs(blob, fileName);
    
    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting to DOCX:", error);
    return Promise.reject(error);
  }
};

/**
 * Export content to PDF file
 * 
 * @param title Document title
 * @param content Markdown content
 * @param fileName Output file name
 */
const exportMarkdownToPdf = async (title: string, content: string, fileName: string): Promise<void> => {
  // In a real implementation, you would use a PDF generation library or service
  // For example, using jsPDF or a server-side service
  
  try {
    // Make sure filename has .pdf extension
    if (!fileName.toLowerCase().endsWith('.pdf')) {
      fileName = `${fileName}.pdf`;
    }
    
    // Placeholder for PDF generation
    // In a real implementation, this would convert the document to PDF
    console.log('PDF export would be implemented with a PDF generation library');
    
    // Use the legacy PDF export for now as a fallback
    const generatedSections = [
      {
        id: 'content',
        name: 'Content',
        description: 'Document Content',
        status: 'completed' as const,
        icon: {} as any,
        content: content
      }
    ];
    
    exportToPDF(title, [], generatedSections);
    
    // Placeholder for success
    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting to PDF:", error);
    return Promise.reject(error);
  }
};

// Export the service
export const documentExportService = {
  // Legacy exports (preserved for backward compatibility)
  exportToWord,
  exportToPDF,
  
  // Enhanced exports for markdown documents
  exportMarkdownToDocx,
  exportMarkdownToPdf
};
