# AI Book Generator - Usage Guide

## Quick Start

### 1. Access the Book Generator
- Navigate to the Research Dashboard
- Click on "AI Book Generator" in the sidebar
- The book generator will open with the metadata form

### 2. Define Your Book
Fill out the book metadata form with:
- **Title & Subtitle**: Your book's main title and optional subtitle
- **Genre**: Select from 20+ available genres (Fiction, Non-Fiction, Academic, etc.)
- **Target Audience**: Choose your intended readers (Students, Professionals, General Public, etc.)
- **Writing Tone**: Select tone (Academic, Professional, Casual, Narrative)
- **Estimated Length**: Choose book size (Short: 50-100 pages, Medium: 100-300 pages, Long: 300+ pages)
- **Description**: Detailed description of your book's purpose and scope
- **Keywords**: Add relevant keywords for context
- **Authors**: List book authors

### 3. Create Chapter Structure
- Click "Next: Define Chapters" to proceed
- Add chapters using the "Add Chapter" button
- For each chapter:
  - Set chapter title and description
  - Define estimated word count (2000-8000 words recommended)
  - Create detailed outlines with sub-sections
  - Add user content (text and images) to guide AI generation

### 4. Configure AI Settings
- Select your preferred AI model (Claude, GPT-4, Gemini, or Llama)
- Each model has different capabilities and token limits
- Claude 3.5 Sonnet is recommended for long-form content

### 5. Generate Your Book
- Click "Generate Book" to start the AI writing process
- Monitor progress in real-time with the generation panel
- View detailed progress for each chapter and section
- Track word count and citation extraction

### 6. Review and Export
- Use the tabbed interface to review:
  - **Generation Details**: Progress and status of each section
  - **Content Preview**: Generated book content with formatting
  - **Citations & References**: Extracted citations and bibliography
- Export your book in multiple formats (DOCX, PDF, HTML, EPUB)
- Edit in the main editor for further refinement

## Advanced Features

### Context-Aware Generation
The book generator uses a sophisticated rolling context system:
- **Complete Context**: First chapter generated with full book outline
- **Rolling Summaries**: Subsequent chapters use summaries of previous chapters (≤400 words)
- **Context Optimization**: Automatically manages token limits by removing old context
- **Narrative Continuity**: Maintains story/argument flow across all chapters

### Chapter Organization
- **Hierarchical Structure**: Support for multi-level sub-sections (1.1, 1.1.1, etc.)
- **Flexible Numbering**: Choose numeric, Roman, or no chapter numbering
- **Content Integration**: Combine user-provided content with AI generation
- **Outline Validation**: Ensure comprehensive chapter structure before generation

### Citation Management
- **Automatic Extraction**: AI-generated content includes academic citations
- **Real Citations**: No placeholder references - only legitimate academic sources
- **Deduplication**: Automatically removes duplicate references
- **Bibliography Generation**: Creates properly formatted reference list
- **Chapter Tracking**: Shows which chapters contain which citations

### Export Options
- **Multiple Formats**: DOCX, PDF, HTML, and EPUB support
- **Book Structure**: Include/exclude table of contents, glossary, index, bibliography
- **Formatting**: Choose chapter numbering style and page numbering
- **Editor Integration**: Send directly to main editor for further editing

## Best Practices

### Planning Your Book
1. **Start with Clear Objectives**: Define what readers should learn or gain
2. **Detailed Outlines**: Provide comprehensive chapter descriptions and sub-sections
3. **Logical Flow**: Organize chapters in a logical progression
4. **Appropriate Length**: Match chapter word counts to content complexity

### Content Preparation
1. **User Content**: Provide relevant text, examples, or images for each chapter
2. **Context Clues**: Include specific details about concepts, examples, or case studies
3. **Tone Consistency**: Ensure your inputs match the selected writing tone
4. **Citation Guidance**: Mention specific areas where citations would be valuable

### Generation Strategy
1. **Model Selection**: Choose AI models based on your content type:
   - **Claude 3.5 Sonnet**: Best for long-form, academic content
   - **GPT-4o**: Good for creative and structured content
   - **Gemini 2.5 Flash**: Fast generation with multi-modal support
   - **Llama 3.1 405B**: Excellent for technical and detailed analysis

2. **Context Management**: 
   - Keep chapter descriptions concise but informative
   - Monitor context token usage in the generation panel
   - Allow the system to optimize context automatically

3. **Quality Control**:
   - Review generated content in the preview tab
   - Check citation quality and relevance
   - Use the editor integration for refinements

### Troubleshooting

#### Generation Issues
- **Slow Generation**: Try a faster model like Gemini 2.5 Flash
- **Context Errors**: Reduce chapter count or shorten descriptions
- **Poor Quality**: Provide more detailed outlines and user content
- **API Errors**: Check internet connection and API key configuration

#### Content Quality
- **Inconsistent Tone**: Review and adjust user content to match selected tone
- **Missing Citations**: Ensure academic content includes reference requirements
- **Repetitive Content**: Provide more diverse examples and perspectives in outlines

#### Export Problems
- **Large File Size**: Consider splitting into multiple volumes
- **Formatting Issues**: Use the editor integration for manual formatting
- **Missing Sections**: Verify all chapters completed generation successfully

## Integration with Research Workflow

### Paper vs Book Generator
- **Papers**: Use AI Paper Generator for academic papers, research articles
- **Books**: Use AI Book Generator for comprehensive, multi-chapter works
- **Shared Features**: Both use same citation extraction and export systems

### Editor Integration
- Generated books can be sent directly to the main editor
- Full editing capabilities available for refinement
- Citation management integrated across all tools

### Citation Workflow
1. Generate book with AI Book Generator
2. Review citations in the Citations tab
3. Validate and enhance references in Citation Manager
4. Export final book with verified bibliography

## Performance Optimization

### For Large Books
- Generate chapters in batches if needed
- Monitor memory usage during generation
- Use appropriate AI models for content length
- Consider breaking very large books into volumes

### Context Efficiency
- Keep chapter summaries under 400 words
- Limit active context to 5 previous chapters
- Allow automatic context optimization
- Monitor token usage in generation panel

### Quality vs Speed
- **High Quality**: Use Claude 3.5 Sonnet with detailed outlines
- **Balanced**: Use GPT-4o with moderate detail
- **Fast Generation**: Use Gemini 2.5 Flash with concise outlines

## Support and Resources

### Getting Help
- Check the README.md for technical details
- Review test files for usage examples
- Use the integrated help tooltips in the interface

### Customization
- Modify constants.ts for different genres or audiences
- Adjust prompts.ts for specialized content types
- Extend types.ts for additional metadata fields

### Contributing
- Follow the established architectural patterns
- Add tests for new features
- Update documentation for changes
- Maintain consistency with paper generator patterns
