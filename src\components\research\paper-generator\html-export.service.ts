import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, BorderStyle, Table, TableRow, TableCell, WidthType, AlignmentType } from 'docx';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

/**
 * Converts HTML content from TipTap editor to a Word document
 * This is an enhanced version that handles more HTML elements properly
 */
const htmlToDocx = async (title: string, htmlContent: string): Promise<Document> => {
  // Create document
  const documentChildren = [];
  
  // Font configuration
  const FONT = {
    name: "Times New Roman",
    ascii: "Times New Roman",
    eastAsia: "Times New Roman",
    hAnsi: "Times New Roman"
  };
  
  // Add title
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 36,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { after: 300 },
    })
  );

  // Create a sandbox div to parse the HTML content
  const tempDiv = document.createElement('div');
  
  // Clean the HTML content a bit to ensure proper parsing
  const cleanedHtml = htmlContent
    .replace(/<p><\/p>/g, '<p>&nbsp;</p>') // Empty paragraphs should have a non-breaking space
    .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>'); // Paragraphs with just BR should have a non-breaking space
  
  tempDiv.innerHTML = cleanedHtml;
  
  // Process each top-level element 
  Array.from(tempDiv.childNodes).forEach((node) => {
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();
      const text = element.textContent || '';
      
      // Skip empty elements
      if (!text.trim()) return;
      
      // Handle different HTML elements
      switch (tagName) {
        case 'h1':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 32,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 240, after: 120 }
            })
          );
          break;
        case 'h2':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 28,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_2,
              spacing: { before: 200, after: 100 }
            })
          );
          break;
        case 'h3':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 24,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_3,
              spacing: { before: 160, after: 80 }
            })
          );
          break;
        case 'p':
          // Check for text formatting
          if (element.querySelector('strong, b')) {
            // Handle paragraphs with bold text
            const boldTexts = element.querySelectorAll('strong, b');
            const textParts = [];
            let lastIndex = 0;
            
            boldTexts.forEach(boldElement => {
              const plainText = element.textContent?.substring(lastIndex, element.textContent.indexOf(boldElement.textContent || '', lastIndex)) || '';
              if (plainText) {
                textParts.push(new TextRun({ text: plainText, color: "000000", font: FONT, size: 24 }));
              }
              
              textParts.push(new TextRun({
                text: boldElement.textContent || '',
                bold: true,
                color: "000000",
                font: FONT,
                size: 24
              }));
              
              lastIndex = element.textContent?.indexOf(boldElement.textContent || '', lastIndex) + (boldElement.textContent?.length || 0) || 0;
            });
            
            // Add any remaining text
            if (lastIndex < (element.textContent?.length || 0)) {
              textParts.push(new TextRun({ text: element.textContent?.substring(lastIndex) || '', color: "000000", font: FONT, size: 24 }));
            }
            
            documentChildren.push(
              new Paragraph({
                children: textParts,
                spacing: { after: 120 }
              })
            );
          } else {
            // Simple paragraph
            documentChildren.push(
              new Paragraph({
                children: [
                  new TextRun({ text, color: "000000", font: FONT, size: 24 })
                ],
                spacing: { after: 120 }
              })
            );
          }
          break;
        case 'ul':
        case 'ol':
          // Handle lists - improved implementation
          const listItems = Array.from(element.children);
          listItems.forEach((li, index) => {
            const listText = li.textContent || '';
            if (listText.trim()) {
              documentChildren.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: tagName === 'ul' ? `• ${listText}` : `${index + 1}. ${listText}`,
                      color: "000000",
                      font: FONT,
                      size: 24
                    })
                  ],
                  indent: { left: 360 },
                  spacing: { after: 80 }
                })
              );
            }
          });
          break;
        case 'table':
          // Handle tables
          const tableRows = Array.from(element.querySelectorAll('tr'));
          if (tableRows.length > 0) {
            const docxTable = new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              rows: tableRows.map((row, rowIndex) => {
                const cells = Array.from(row.querySelectorAll('td, th'));
                return new TableRow({
                  children: cells.map(cell => new TableCell({
                    children: [new Paragraph({
                      children: [
                        new TextRun({
                          text: cell.textContent || '',
                          color: "000000",
                          font: FONT,
                          size: 24
                        })
                      ],
                      alignment: AlignmentType.LEFT
                    })],
                    width: {
                      size: 100 / cells.length,
                      type: WidthType.PERCENTAGE,
                    }
                  }))
                });
              })
            });
            documentChildren.push(docxTable);
          }
          break;
        case 'blockquote':
          // Create a quote paragraph with left border
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({ text, color: "000000", font: FONT, size: 24 })
              ],
              border: {
                left: {
                  color: "999999",
                  size: 10,
                  style: BorderStyle.SINGLE,
                  space: 5
                }
              },
              spacing: { after: 120 },
              indent: { left: 240 },
              style: "Quote"
            })
          );
          break;
        default:
          // Default to paragraph for unknown elements
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({ text, color: "000000", font: FONT, size: 24 })
              ],
              spacing: { after: 120 }
            })
          );
      }
    }
  });
  
  return new Document({
    sections: [
      {
        properties: {},
        children: documentChildren,
      },
    ],
  });
};

/**
 * Export document as DOCX
 */
const exportToDocx = async (title: string, htmlContent: string, fileName: string = "document.docx"): Promise<void> => {
  try {
    // Create a more reliable method for processing the content
    const doc = await htmlToDocx(title, htmlContent);
    // Use toBlob for browser compatibility
    const blob = await Packer.toBlob(doc);
    const finalFileName = fileName.endsWith('.docx') ? fileName : `${fileName}.docx`;
    
    // Use the proper saveAs function from file-saver
    saveAs(blob, finalFileName);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error exporting to DOCX:', error);
    return Promise.reject(error);
  }
};

/**
 * Export document as PDF using jsPDF
 */
const exportToPdf = async (title: string, htmlContent: string, fileName: string = "document.pdf"): Promise<void> => {
  try {
    // Create a temporary container for rendering
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '0';
    tempContainer.style.width = '210mm'; // A4 width
    tempContainer.style.background = 'white';
    tempContainer.style.padding = '20mm';
    tempContainer.style.fontFamily = 'Georgia, "Times New Roman", serif';
    tempContainer.style.fontSize = '12pt';
    tempContainer.style.lineHeight = '1.6';
    tempContainer.style.color = '#000';
    
    // Add title and content
    tempContainer.innerHTML = `
      <h1 style="font-size: 18pt; margin-bottom: 24pt; border-bottom: 2px solid #ccc; padding-bottom: 8pt;">${title}</h1>
      ${htmlContent}
    `;
    
    document.body.appendChild(tempContainer);
    
    // Create PDF using html2canvas and jsPDF
    const canvas = await html2canvas(tempContainer, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: tempContainer.scrollWidth,
      height: tempContainer.scrollHeight
    });
    
    // Remove temporary container
    document.body.removeChild(tempContainer);
    
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;
    
    // Add first page
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;
    
    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }
    
    // Save the PDF
    pdf.save(fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return Promise.reject(error);
  }
};

export const documentExportService = {
  exportToDocx,
  exportToPdf,
  htmlToDocx
};
