import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Plus, BookOpen, Users, Target, Lightbulb, FileText } from "lucide-react";
import { BookMetadata } from '../types';
import { BOOK_GENRES, TARGET_AUDIENCES, BOOK_TONES, BOOK_LENGTHS } from '../constants';

interface BookMetadataFormProps {
  metadata: BookMetadata;
  onMetadataChange: (metadata: BookMetadata) => void;
  onNext: () => void;
}

export const BookMetadataForm: React.FC<BookMetadataFormProps> = ({
  metadata,
  onMetadataChange,
  onNext
}) => {
  const [newKeyword, setNewKeyword] = useState('');
  const [newAuthor, setNewAuthor] = useState('');

  const handleInputChange = (field: keyof BookMetadata, value: any) => {
    onMetadataChange({
      ...metadata,
      [field]: value
    });
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !metadata.keywords.includes(newKeyword.trim())) {
      handleInputChange('keywords', [...metadata.keywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    handleInputChange('keywords', metadata.keywords.filter(k => k !== keyword));
  };

  const addAuthor = () => {
    if (newAuthor.trim() && !metadata.authors.includes(newAuthor.trim())) {
      handleInputChange('authors', [...metadata.authors, newAuthor.trim()]);
      setNewAuthor('');
    }
  };

  const removeAuthor = (author: string) => {
    handleInputChange('authors', metadata.authors.filter(a => a !== author));
  };

  const isFormValid = () => {
    return metadata.title.trim() !== '' && 
           metadata.genre !== '' && 
           metadata.targetAudience !== '' &&
           metadata.tone !== '' &&
           metadata.description.trim() !== '' &&
           metadata.estimatedLength !== '';
  };

  const selectedTone = BOOK_TONES.find(tone => tone.id === metadata.tone);
  const selectedLength = BOOK_LENGTHS.find(length => length.id === metadata.estimatedLength);

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
          <BookOpen className="h-10 w-10 text-blue-600" />
          AI Book Generator
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Create comprehensive, well-structured books with AI assistance. Start by providing your book's basic information and structure.
        </p>
      </div>

      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            Book Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Title and Subtitle */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Book Title *
              </Label>
              <Input
                id="title"
                placeholder="Enter your book title..."
                value={metadata.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="text-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subtitle" className="text-sm font-medium">
                Subtitle (Optional)
              </Label>
              <Input
                id="subtitle"
                placeholder="Enter subtitle if applicable..."
                value={metadata.subtitle || ''}
                onChange={(e) => handleInputChange('subtitle', e.target.value)}
              />
            </div>
          </div>

          {/* Genre and Target Audience */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Genre *
              </Label>
              <Select value={metadata.genre} onValueChange={(value) => handleInputChange('genre', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select book genre..." />
                </SelectTrigger>
                <SelectContent>
                  {BOOK_GENRES.map((genre) => (
                    <SelectItem key={genre} value={genre}>
                      {genre}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Target Audience *
              </Label>
              <Select value={metadata.targetAudience} onValueChange={(value) => handleInputChange('targetAudience', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target audience..." />
                </SelectTrigger>
                <SelectContent>
                  {TARGET_AUDIENCES.map((audience) => (
                    <SelectItem key={audience} value={audience}>
                      {audience}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tone and Length */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Target className="h-4 w-4" />
                Writing Tone *
              </Label>
              <Select value={metadata.tone} onValueChange={(value) => handleInputChange('tone', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select writing tone..." />
                </SelectTrigger>
                <SelectContent>
                  {BOOK_TONES.map((tone) => (
                    <SelectItem key={tone.id} value={tone.id}>
                      <div>
                        <div className="font-medium">{tone.name}</div>
                        <div className="text-sm text-gray-500">{tone.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedTone && (
                <p className="text-sm text-gray-600">{selectedTone.description}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Estimated Length *
              </Label>
              <Select value={metadata.estimatedLength} onValueChange={(value) => handleInputChange('estimatedLength', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select book length..." />
                </SelectTrigger>
                <SelectContent>
                  {BOOK_LENGTHS.map((length) => (
                    <SelectItem key={length.id} value={length.id}>
                      <div>
                        <div className="font-medium">{length.name}</div>
                        <div className="text-sm text-gray-500">{length.wordCount} • {length.chapters}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedLength && (
                <p className="text-sm text-gray-600">{selectedLength.wordCount} • {selectedLength.chapters}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Book Description *
            </Label>
            <Textarea
              id="description"
              placeholder="Provide a detailed description of your book's purpose, scope, and main themes..."
              value={metadata.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-sm text-gray-500">
              This description will help the AI understand your book's context and maintain consistency throughout.
            </p>
          </div>

          {/* Keywords */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Keywords</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Add a keyword..."
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                className="flex-1"
              />
              <Button onClick={addKeyword} size="sm" variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {metadata.keywords.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {metadata.keywords.map((keyword) => (
                  <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                    {keyword}
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeKeyword(keyword)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Authors */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Authors</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Add an author..."
                value={newAuthor}
                onChange={(e) => setNewAuthor(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addAuthor()}
                className="flex-1"
              />
              <Button onClick={addAuthor} size="sm" variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {metadata.authors.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {metadata.authors.map((author) => (
                  <Badge key={author} variant="secondary" className="flex items-center gap-1">
                    {author}
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeAuthor(author)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Next Button */}
      <div className="flex justify-end">
        <Button 
          onClick={onNext}
          disabled={!isFormValid()}
          size="lg"
          className="px-8"
        >
          Next: Define Chapters
        </Button>
      </div>
    </div>
  );
};
