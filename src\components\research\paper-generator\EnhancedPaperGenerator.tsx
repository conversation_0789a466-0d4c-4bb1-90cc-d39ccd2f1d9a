import React, { useState, useEffect } from 'react';
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, GripVertical } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { UserInputs, UserSection, ContentItem, GeneratedSection, PaperMetadata } from './types';
import { SECTION_TYPES, SECTION_PROMPTS, AI_MODELS } from './constants';
import { AIModelSelector } from './AIModelSelector';
import { PaperMetadataForm } from './PaperMetadataForm';
import { SectionCard } from './SectionCard';
import { GenerationPanel } from './GenerationPanel';
import paperAIService from './paper-ai.service';
import { editorService } from './editor.service';
import { MainEditor } from '../MainEditor';
// Using the enhanced citation extraction service for better reference matching
import { extractCitationsFromText, matchCitationsWithReferences } from './citation-extraction.enhanced';
import { Citation } from './types';
import { separateSectionReferences } from './section-utils';

export function EnhancedPaperGenerator() {

  // State for user inputs with metadata
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      researchField: "",
      keywords: [],
      authors: []
    },
    userSections: []
  });

  // Track which sections have been generated
  const [generatedSections, setGeneratedSections] = useState<GeneratedSection[]>(
    SECTION_TYPES
      .filter(type => type.id !== 'keywords') // Keywords aren't a generated section
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        status: 'pending',
        icon: type.icon
      }))
  );

  // UI state
  const [currentStep, setCurrentStep] = useState<'input' | 'generation' | 'editor'>('input');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);
  const [editorContent, setEditorContent] = useState<{ title: string; content: string } | null>(null);
  
  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [sectionCitations, setSectionCitations] = useState<Record<string, string[]>>({});

  // Helper functions to check if we can proceed with generation
  const hasRequiredSections = () => {
    const requiredSectionIds = SECTION_TYPES
      .filter(type => type.required)
      .map(type => type.id);
      
    return requiredSectionIds.every(id => 
      userInputs.userSections.some(section => 
        section.name.toLowerCase() === SECTION_TYPES.find(t => t.id === id)?.name.toLowerCase()
      )
    );
  };

  const sectionsHaveContent = () => {
    return userInputs.userSections.every(section => section.items.length > 0);
  };

  const hasTitle = () => {
    return userInputs.metadata.title.trim() !== "";
  };

  // Only title is required to proceed
  const canProceed = hasTitle();

  // Metadata management
  const updateMetadata = (metadata: Partial<PaperMetadata>) => {
    setUserInputs(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        ...metadata
      }
    }));
  };

  // Section management
  const addUserSection = (sectionType: string) => {
    const sectionTemplate = SECTION_TYPES.find(s => s.id === sectionType);
    if (!sectionTemplate) return;

    // Check if section already exists to prevent duplicates
    if (userInputs.userSections.some(s => s.name === sectionTemplate.name)) {
      toast.error(`${sectionTemplate.name} section already exists.`);
      return;
    }

    const newSection: UserSection = {
      id: Date.now().toString(),
      name: sectionTemplate.name,
      items: []
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: [...prev.userSections, newSection]
    }));
  };

  const removeUserSection = (sectionId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.filter(section => section.id !== sectionId)
    }));
  };

  // Content item management
  const addContentItem = (sectionId: string, type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: Date.now().toString(),
      type,
      content: '',
      order: 0,
      ...(type === 'figure' && { title: '', caption: '' })
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: [...section.items, { ...newItem, order: section.items.length }] }
          : section
      )
    }));
  };

  const updateContentItem = (sectionId: string, itemId: string, updates: Partial<ContentItem>) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              items: section.items.map(item => 
                item.id === itemId ? { ...item, ...updates } : item
              )
            }
          : section
      )
    }));
  };

  const removeContentItem = (sectionId: string, itemId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: section.items.filter(item => item.id !== itemId) }
          : section
      )
    }));
  };

  const moveContentItem = (sectionId: string, itemId: string, direction: 'up' | 'down') => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => {
        if (section.id !== sectionId) return section;
        
        const items = [...section.items];
        const index = items.findIndex(item => item.id === itemId);
        
        if (direction === 'up' && index > 0) {
          [items[index], items[index - 1]] = [items[index - 1], items[index]];
        } else if (direction === 'down' && index < items.length - 1) {
          [items[index], items[index + 1]] = [items[index + 1], items[index]];
        }
        
        return { ...section, items };
      })
    }));
  };

  // Helper to get section content as a string
  const getSectionContent = (sectionName: string): string => {
    const section = userInputs.userSections.find(
      s => s.name.toLowerCase() === sectionName.toLowerCase()
    );
    
    if (!section || !section.items.length) {
      return `[AI will generate ${sectionName} content]`;
    }
    
    // Remove redundant headings from user content (e.g., "Introduction", "Methodology", etc.)
    const headingPattern = new RegExp(`^\\s*(${SECTION_TYPES.map(s => s.name).join('|')})\\s*\\n+`, 'i');
    return section.items
      .map(item => {
        if (item.type === 'text') {
          // Remove section heading if present at the start
          return item.content.replace(headingPattern, '').trim();
        } else {
          return `Figure: ${item.title || 'Untitled'}\n${item.caption || 'No caption'}`;
        }
      })
      .join('\n\n');
  };

  // Generation logic
  const generateSection = async (sectionId: string) => {
    // Mark section as generating
    setGeneratedSections(prev => prev.map(s => 
      s.id === sectionId ? { ...s, status: 'generating' } : s
    ));

    try {
      const { title, researchField, keywords } = userInputs.metadata;
      let prompt = '';
      let generatedContent = '';

      // Always generate a section, even if user content is missing
      switch (sectionId) {
        case 'introduction':
          prompt = SECTION_PROMPTS.introduction(title, researchField || '', keywords || []);
          break;

        case 'methodology':
          prompt = SECTION_PROMPTS.methodology(title, researchField || '', getSectionContent('Methodology') || '');
          break;
        case 'results':
          prompt = SECTION_PROMPTS.results(title, getSectionContent('Methodology') || '', getSectionContent('Results') || '');
          break;
        case 'discussion':
          prompt = SECTION_PROMPTS.discussion(title, getSectionContent('Results') || '', getSectionContent('Methodology') || '');
          break;
        case 'conclusion':
          prompt = SECTION_PROMPTS.conclusion(title, getSectionContent('Results') || '', getSectionContent('Discussion') || '');
          break;
        case 'abstract':
          prompt = SECTION_PROMPTS.abstract(
            title,
            getGeneratedSectionContent('introduction') || '',
            getGeneratedSectionContent('methodology') || '',
            getGeneratedSectionContent('results') || '',
            getGeneratedSectionContent('conclusion') || ''
          );
          break;
        case 'references':
          // Generate references directly from extracted citations instead of using AI
          console.log('Generating references section from extracted citations');
          console.log(`Found ${allCitations.length} total citations`);
          
          // Use the generateReferencesFromCitations helper function
          const referencesContent = generateReferencesFromCitations();
          
          // Update the section directly without calling the AI service
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: referencesContent
            } : s
          ));
          
          // Return early to skip the AI call
          return;
        default:
          prompt = `Generate a ${sectionId} section for the research paper titled "${title}".`;
      }

      generatedContent = await paperAIService.generatePaperSection(prompt, {
        model: selectedModel,
        // Remove token limits to ensure complete content generation
        maxTokens: 4096
      });

      // Clean and update the section with generated content
      const cleanedContent = cleanAcademicContent(generatedContent);
      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'completed', content: cleanedContent } : s
      ));
      
      // For non-reference sections, extract citations and their references
      if (sectionId !== 'references' && generatedContent) {
        // Separate main content from any references section that might be included
        const { mainContent, extractedReferences } = separateSectionReferences(generatedContent);

        // Update the section content to only show main content (without references)
        setGeneratedSections(prev => prev.map(s =>
          s.id === sectionId ? { ...s, content: mainContent } : s
        ));

        console.log(`Separated content for ${sectionId}: ${extractedReferences.length} references found`);

        // Extract citations from the main content only
        const extractedInfo = extractCitationsFromText(mainContent, sectionId);

        if (extractedInfo.citations.length > 0) {
          console.log(`Found ${extractedInfo.citations.length} citations in ${sectionId} section`);

          // Update section citations map
          setSectionCitations(prev => ({
            ...prev,
            [sectionId]: extractedInfo.citations.map(c => c.id)
          }));

          // Create citations with reference data from extracted references
          const citationsWithRefs = extractedInfo.citations.map(citation => {
            // Try to find a matching reference in the extracted references
            let matchingRef = '';

            if (extractedReferences.length > 0) {
              const authorLastName = citation.authors[0].split(' ')[0].toLowerCase();
              const yearStr = citation.year.toString();

              matchingRef = extractedReferences.find(ref =>
                ref.toLowerCase().includes(authorLastName) && ref.includes(yearStr)
              ) || '';
            }

            return {
              ...citation,
              referenceText: matchingRef
            };
          });

          // Merge with existing citations
          setAllCitations(prev => {
            // Check for existing citations with same ID or in-text format
            const newCitations = citationsWithRefs.filter(newCitation =>
              !prev.some(existing =>
                existing.id === newCitation.id ||
                existing.inTextFormat === newCitation.inTextFormat
              )
            );

            // Update existing citations to include this section ID if they're found in this section
            const updatedExisting = prev.map(citation => {
              const matchingNew = citationsWithRefs.find(c =>
                c.id === citation.id ||
                c.inTextFormat === citation.inTextFormat
              );

              if (matchingNew) {
                // Add this section to the citation's sectionIds if not already there
                // And update the referenceText if we found one and it doesn't already have one
                if (!citation.sectionIds.includes(sectionId)) {
                  return {
                    ...citation,
                    sectionIds: [...citation.sectionIds, sectionId],
                    referenceText: citation.referenceText || matchingNew.referenceText
                  };
                }
              }

              return citation;
            });

            // Return updated citations plus any new ones
            return [...updatedExisting, ...newCitations];
          });
        }
        
        // After extracting citations from a content section, try to match with references if we have any
        // This helps build up the reference text as we go
        setAllCitations(prev => {
          // Only try to match if we have citations
          if (prev.length === 0) return prev;
          
          // Get all existing references content, even from incomplete sections
          let referencesContent = '';
          const refsSection = generatedSections.find(s => s.id === 'references');
          if (refsSection?.content) {
            referencesContent = refsSection.content;
          } else {
            // Don't use default references as a fallback
            referencesContent = "";
          }
          
          // Match citations with references
          const updatedCitations = matchCitationsWithReferences(prev, referencesContent);
          
          // Log how many citations have references
          const withRefs = updatedCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
          console.log(`After matching: ${withRefs}/${updatedCitations.length} citations have reference text`);
          
          return updatedCitations;
        });
      }
      
      // For references section, match with existing citations
      if (sectionId === 'references' && generatedContent) {
        // Update citations with reference details
        const updatedCitations = matchCitationsWithReferences(allCitations, generatedContent);
        const withRefs = updatedCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
        
        console.log(`Matched references: ${withRefs}/${updatedCitations.length} citations have reference text`);
        
        setAllCitations(updatedCitations);          // We no longer try to match with default references
          // Instead, we'll rely only on the references naturally generated by the AI model
        
        // Generate a fresh references section from the updated citations
        const freshReferences = generateReferencesFromCitations();
        
        // Always update the references section to ensure we're showing the most current data
        console.log('Updating references section with matched citations');
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: freshReferences
          } : s
        ));
      }
    } catch (error) {
      console.error(`Error generating ${sectionId}:`, error);
      setGeneratedSections(prev => prev.map(s => 
        s.id === sectionId ? { ...s, status: 'error' } : s
      ));
      toast.error(`Failed to generate ${sectionId} section. Please try again.`);
    }
  };
  
  // Helper function to get the content of generated sections
  const getGeneratedSectionContent = (sectionId: string): string => {
    const section = generatedSections.find(s => s.id === sectionId && s.status === 'completed');
    // Add logging to debug references issues
    console.log(`Getting content for ${sectionId}: ${section ? 'Content found' : 'No content'} (${section?.content?.substring(0, 20)}...)`);
    return section?.content || '';
  };

  // Generate all sections in order
  const generateAllSections = async () => {
    if (!canProceed) {
      toast.error("Please provide a title for your research paper.");
      return;
    }
    
    setIsGenerating(true);
    setCurrentStep('generation');

    // Sort sections by logical order, except references which will be generated last
    const orderedSections = [...generatedSections]
      .filter(section => section.id !== 'references') // Remove references first
      .sort((a, b) => {
        const aType = SECTION_TYPES.find(t => t.id === a.id);
        const bType = SECTION_TYPES.find(t => t.id === b.id);
        return (aType?.order || 0) - (bType?.order || 0);
      });
    
    // Find the references section
    const referencesSection = generatedSections.find(s => s.id === 'references');

    try {
      // Generate all main sections first
      const generatedContentSections = [];
      for (const section of orderedSections) {
        try {
          await generateSection(section.id);
          generatedContentSections.push(section.id);
          // Add a small delay to ensure the UI updates properly
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (sectionError) {
          console.error(`Error generating ${section.id} section:`, sectionError);
          toast.error(`Failed to generate ${section.name} section.`);
        }
      }
      
      console.log(`Successfully generated ${generatedContentSections.length} content sections: ${generatedContentSections.join(', ')}`);
      
      // Always generate a references section, even if no content sections have been generated
      if (referencesSection) {
        toast.info("Compiling references from all in-text citations...");
        
        try {
          // Force a longer delay before generating references to ensure state is updated and content is available
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // We'll no longer try to match with default references
          if (allCitations.length > 0) {
            console.log(`Processing ${allCitations.length} citations for references`);
            
            // Log how many citations already have references
            const withRefs = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
            console.log(`${withRefs}/${allCitations.length} citations have reference text`);
            
            // Short delay to ensure state is updated
            await new Promise(resolve => setTimeout(resolve, 300));
          }
          
          // Generate references directly from the extracted citations
          const referencesContent = generateReferencesFromCitations();
          
          // Update the section state
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: referencesContent
            } : s
          ));
          
          // Log the first 100 chars of references content
          console.log(`Generated references section (${referencesContent.length} chars): ${referencesContent.substring(0, 100)}...`);
          
          const citationsWithRefs = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
          toast.success(`References compiled successfully! Found ${citationsWithRefs} citations with reference data.`);
        } catch (refsError) {
          console.error("Error processing references:", refsError);
          toast.error("Failed to compile references. No default references will be shown.");
          
          // Add an error message for references if generation failed
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: "No references could be extracted from this paper. Please ensure your sections include proper citations that can be matched with references."
            } : s
          ));
        }
      } else {
        console.error("References section not found in generatedSections");
      }
      
      toast.success("Your research paper has been generated successfully!");
    } catch (error) {
      console.error("Error generating paper:", error);
      toast.error("There was an error generating your paper. Please try again.");
      
      // Add an error message for references section
      if (referencesSection) {
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: "Cannot generate references because there was an error generating the paper content. Please try again."
          } : s
        ));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Export the paper
  const handleExport = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to export.");
      return;
    }
    
    // We'll let the ExportDialog handle the actual export functionality
    // This function is now primarily used as a callback for the button in GenerationPanel
    toast.info("Use the export dialog to choose your preferred format.");
  };

  // Helper function to clean and format academic content
  const cleanAcademicContent = (content: string): string => {
    return content
      // Convert markdown headers to bold text
      .replace(/^#{1,6}\s+(.+)$/gm, '**$1**')
      // Fix citation formatting - ensure proper capitalization
      .replace(/\([A-Z][A-Z\s,&]+,\s*\d{4}\)/g, (match) => {
        // Convert all-caps citations to proper case
        return match.replace(/[A-Z]{2,}/g, (caps) => {
          return caps.charAt(0) + caps.slice(1).toLowerCase();
        });
      })
      // Make common subheadings bold if they're not already
      .replace(/^(Abstract|Introduction|Methodology|Methods|Results|Discussion|Conclusion|References|Background|Objectives?|Aims?|Literature Review|Data Analysis|Statistical Analysis|Findings|Implications|Limitations|Future Work)$/gm, '**$1**')
      // Clean up extra whitespace
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  };

  // Generate references section from collected citations
  const generateReferencesFromCitations = () => {
    console.log(`Generating references from ${allCitations.length} citations`);

    // First check if we have any citations
    if (allCitations.length === 0) {
      toast.warning("No citations found in the paper.");
      console.log("No citations found, returning empty references");
      return "No citations were found in this paper.";
    }

    // Collect all references from all sections that were extracted during generation
    const allSectionRefs = new Set<string>();

    // Extract references from each completed section (excluding the references section itself)
    generatedSections.forEach(section => {
      if (section.status === 'completed' && section.content && section.id !== 'references') {
        // Get the original content that might contain references
        const originalContent = section.content;

        // Use the separateSectionReferences utility to extract references properly
        const { extractedReferences } = separateSectionReferences(originalContent);

        console.log(`Section ${section.id}: Found ${extractedReferences.length} references`);

        // Add each extracted reference to our set
        extractedReferences.forEach(ref => {
          if (ref && ref.trim().length > 30) {
            allSectionRefs.add(ref.trim());
          }
        });

        // If no references were found with the utility, try a more aggressive approach
        if (extractedReferences.length === 0) {
          const lines = originalContent.split('\n');
          let inReferencesSection = false;

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Check if we're entering a references section
            if (line.match(/^references\s*:?$/i) || line.match(/^bibliography\s*:?$/i)) {
              inReferencesSection = true;
              continue;
            }

            // If we're in references section, look for reference-like lines
            if (inReferencesSection && line.length > 30) {
              // Look for lines that look like academic references
              if (line.match(/[A-Z][a-z]+.*\(\d{4}\)/) &&
                  (line.includes('.') || line.includes(',')) &&
                  !line.toLowerCase().startsWith('citation') &&
                  !line.toLowerCase().startsWith('total')) {
                allSectionRefs.add(line.trim());
              }
            }
          }
        }
      }
    });

    console.log(`Found ${allSectionRefs.size} references in section content`);

    // Use citations that already have reference text from the extraction process
    allCitations.forEach(citation => {
      if (citation.referenceText && citation.referenceText.trim() !== '') {
        allSectionRefs.add(citation.referenceText.trim());
      }
    });

    // Match citations with extracted references more intelligently
    allCitations.forEach(citation => {
      if (!citation.referenceText || citation.referenceText.trim() === '') {
        // Try to match with existing references first
        const authorLastName = citation.authors[0].split(' ')[0].toLowerCase();
        const yearStr = citation.year.toString();

        let matchingRef = '';
        for (const ref of allSectionRefs) {
          if (ref.toLowerCase().includes(authorLastName) && ref.includes(yearStr)) {
            matchingRef = ref;
            break;
          }
        }

        if (matchingRef) {
          citation.referenceText = matchingRef;
        }
      }
    });

    // Only generate placeholder references if we have citations but no references at all
    if (allSectionRefs.size === 0 && allCitations.length > 0) {
      console.log("No references found in sections, this indicates the AI didn't generate proper references");
      toast.warning("No references were found in the generated content. Please regenerate the paper to get proper references.");
      return "No references were found in the generated content. Please regenerate the paper sections to get proper academic references.";
    }

    // Create our final references array
    const referencesArray = Array.from(allSectionRefs);

    if (referencesArray.length === 0) {
      toast.error("No references could be generated for the citations in this paper.");
      return "No references could be found or generated for the citations in this paper.";
    }

    // Sort references alphabetically by first author's last name
    const sortedReferences = referencesArray.sort((a, b) => {
      const getFirstAuthorLastName = (ref: string): string => {
        const authorPart = ref.split('(')[0].trim();
        return authorPart.split(',')[0].trim().toLowerCase();
      };
      return getFirstAuthorLastName(a).localeCompare(getFirstAuthorLastName(b));
    });

    // Generate statistics for references and citations
    const totalCitations = allCitations.length;
    const citationsWithReferences = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
    const uniqueAuthorsCount = new Set(allCitations.flatMap(c => c.authors.map(a => a.toLowerCase()))).size;

    // Format the references section in proper academic style (without numbering, as per APA style)
    const referencesText = `${sortedReferences.join('\n\n')}\n\n---\nCitation Statistics: ${totalCitations} total citations | ${citationsWithReferences} with references | ${uniqueAuthorsCount} unique authors`;

    console.log(`Generated references text with ${sortedReferences.length} entries`);

    return referencesText;
  };

  // Edit in main editor
  const handleEditInMainEditor = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to edit.");
      return;
    }
    
    // Only proceed with regenerating references if we have content sections completed
    const contentSections = completedSections.filter(s =>
      ['introduction', 'methodology', 'results', 'discussion', 'conclusion'].includes(s.id)
    );
    
    console.log(`Found ${contentSections.length} completed content sections before editing`);
    
    // Generate references from collected citations
    const referencesContent = generateReferencesFromCitations();
    toast.success(`Compiled ${allCitations.length} citations into references section.`);
    
    // Create or update the references section
    const refsSection = generatedSections.find(s => s.id === 'references');
    
    if (refsSection) {
      // Update existing references section
      setGeneratedSections(prev => prev.map(s => 
        s.id === 'references' ? { 
          ...s, 
          status: 'completed', 
          content: referencesContent
        } : s
      ));
    } else {
      // Create a new references section if it doesn't exist
      setGeneratedSections(prev => [
        ...prev, 
        {
          id: 'references',
          name: 'References',
          description: 'Citations and references used throughout the paper',
          status: 'completed',
          content: referencesContent,
          icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
        }
      ]);
    }
    
    // Small delay to ensure state is updated before sending to editor
    setTimeout(() => sendToEditor(), 500);
  };
  
  // Helper function to send content to editor with properly formatted sections
  const sendToEditor = () => {
    // Get the latest completed sections, including the newly generated references
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    
    // Create a formatted document from all the sections except references
    const mainSections = completedSections
      .filter(s => s.id !== 'references') // Exclude references initially
      .sort((a, b) => {
        // Order should be: Abstract, Introduction, Methodology, Results, Discussion, Conclusion
        const sectionOrder: Record<string, number> = {
          'abstract': 1,
          'introduction': 2,
          'methodology': 3,
          'results': 4,
          'discussion': 5,
          'conclusion': 6
        };
        return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
      })
      .map(section => `**${section.name}**\n\n${section.content}`)
      .join('\n\n');
    
    // Get references section separately to ensure it's always at the end
    let referencesSection = completedSections.find(s => s.id === 'references');
    
    // Generate fresh references from the citations every time
    let referencesContent = generateReferencesFromCitations();
    
    // Log the citations and references status
    console.log(`Using ${allCitations.length} citations to generate references section`);
    console.log(`Citations with reference text: ${allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length}`);
    
    // Ensure we have valid references content
    if (!referencesContent || referencesContent.trim() === '') {
      console.log('No references content generated, creating minimal references section');
      referencesContent = "No references were found in the generated paper content.";
    }

    // Update the references section with the generated content
    if (referencesSection) {
      setGeneratedSections(prev => prev.map(s =>
        s.id === 'references' ? { ...s, content: referencesContent, status: 'completed' as const } : s
      ));
    } else {
      // Create a new references section if none exists
      const newReferencesSection: GeneratedSection = {
        id: 'references',
        name: 'References',
        description: 'Citations and references used throughout the paper',
        status: 'completed',
        content: referencesContent,
        icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
      };

      setGeneratedSections(prev => [...prev, newReferencesSection]);
      referencesSection = newReferencesSection;
    }
    
    // Clean up references content
    // Remove any "References:" header that might be at the beginning
    referencesContent = referencesContent.replace(/^References:[ \t]*[\r\n]+/, '');
    
    // Combine main content with references section always at the end
    // Always include references section, with bold heading
    const rawContent = mainSections + `\n\n**References**\n\n${referencesContent}`;
    const formattedContent = cleanAcademicContent(rawContent);

    try {
      // Instead of trying to navigate, we'll switch to the editor view within this component
      setEditorContent({
        title: userInputs.metadata.title,
        content: formattedContent
      });
      
      // Store the content in localStorage as a backup
      editorService.sendToMainEditor({
        title: userInputs.metadata.title,
        content: formattedContent
      });
      
      // Update UI state to show the editor
      setCurrentStep('editor');
      toast.success("Paper loaded in editor for further editing.");
    } catch (error) {
      console.error('Error loading editor:', error);
      toast.error("An error occurred while loading the editor.");
    }
  };

  // Render the appropriate step based on currentStep
  if (currentStep === 'editor' && editorContent) {
    return (
      <div className="min-h-screen">
        <div className="sticky top-0 z-10 bg-white p-4 border-b shadow-sm flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentStep('generation')}
              className="flex items-center gap-1"
            >
              ← Back to Paper
            </Button>
            <h2 className="font-bold text-lg text-gray-800">{editorContent.title}</h2>
          </div>
          <Button
            variant="default"
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            Save Changes
          </Button>
        </div>
        <MainEditor initialTitle={editorContent.title} initialContent={editorContent.content} />
      </div>
    );
  }

  // Render input step
  if (currentStep === 'input') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                  <Bot className="h-10 w-10 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              AI Research Paper Generator
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Upload your research content with methodology, results, and analysis. Our AI will craft a complete, professional research paper for you.
            </p>
          </div>

          {/* Paper Metadata Section */}
          <PaperMetadataForm 
            metadata={userInputs.metadata}
            updateMetadata={updateMetadata}
          />

          {/* Research Content Sections */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-8">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-3xl flex items-center gap-4">
                    <div className="w-3 h-10 bg-gradient-to-b from-green-500 to-blue-500 rounded-full"></div>
                    Research Content Sections
                  </CardTitle>
                  <CardDescription className="text-lg mt-3 text-gray-600">
                    Build comprehensive research sections with detailed analysis, figures, and supporting content.
                  </CardDescription>
                </div>
                <div className="hidden lg:flex flex-wrap gap-3 max-w-md">
                  {SECTION_TYPES.sort((a, b) => a.order - b.order).map((sectionType) => (
                    <Button 
                      key={sectionType.id}
                      onClick={() => addUserSection(sectionType.id)} 
                      variant="outline" 
                      className="flex items-center gap-2 hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-300 px-4 py-2"
                    >
                      <div className={`w-3 h-3 rounded-full ${sectionType.color}`}></div>
                      <sectionType.icon className="h-4 w-4" />
                      <span className="font-medium">{sectionType.name}</span>
                      {sectionType.required && (
                        <Badge variant="secondary" className="bg-red-100 text-red-800 ml-1">Required</Badge>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Mobile Section Buttons */}
              <div className="lg:hidden flex flex-wrap gap-3 mt-6">
                {SECTION_TYPES.map((sectionType) => (
                  <Button 
                    key={sectionType.id}
                    onClick={() => addUserSection(sectionType.id)} 
                    variant="outline" 
                    className="flex items-center gap-2 hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-300"
                  >
                    <div className={`w-3 h-3 rounded-full ${sectionType.color}`}></div>
                    <sectionType.icon className="h-4 w-4" />
                    {sectionType.name}
                  </Button>
                ))}
              </div>
            </CardHeader>
            
            <CardContent>
              {userInputs.userSections.length === 0 ? (
                <div className="text-center py-20 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl border-2 border-dashed border-gray-200">
                  <div className="relative mb-8">
                    <div className="absolute inset-0 bg-blue-100 rounded-full blur-2xl opacity-40"></div>
                    <GripVertical className="relative h-20 w-20 mx-auto text-gray-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-700 mb-3">Ready to Build Your Research?</h3>
                  <p className="text-gray-500 mb-8 max-w-md mx-auto leading-relaxed">
                    Add research sections to include your methodology, results, analysis, and supporting figures. Each section supports rich content and detailed analysis.
                  </p>
                  <div className="flex justify-center gap-4">
                    <Button 
                      onClick={() => addUserSection('introduction')}
                      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-6 py-3"
                    >
                      {(() => {
                        const IntroIcon = SECTION_TYPES.find(s => s.id === 'introduction')?.icon;
                        return IntroIcon && <IntroIcon className="h-5 w-5" />;
                      })()}
                      Start with Introduction
                    </Button>
                    <Button 
                      onClick={() => addUserSection('methodology')}
                      variant="outline"
                      className="flex items-center gap-2 border-2 hover:border-green-400 px-6 py-3"
                    >
                      {(() => {
                        const MethodIcon = SECTION_TYPES.find(s => s.id === 'methodology')?.icon;
                        return MethodIcon && <MethodIcon className="h-5 w-5" />;
                      })()}
                      Start with Methodology
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-10">
                  {/* Sort sections in logical order based on the SECTION_TYPES order property */}
                  {userInputs.userSections
                    .sort((a, b) => {
                      const aType = SECTION_TYPES.find(t => t.name === a.name);
                      const bType = SECTION_TYPES.find(t => t.name === b.name);
                      return (aType?.order || 0) - (bType?.order || 0);
                    })
                    .map((section) => {
                      const sectionType = SECTION_TYPES.find(s => s.name === section.name);
                      if (!sectionType) return null;
                      
                      return (
                        <SectionCard
                          key={section.id}
                          section={section}
                          sectionColor={sectionType.color}
                          sectionIcon={sectionType.icon}
                          sectionDescription={sectionType.description}
                          removeUserSection={removeUserSection}
                          addContentItem={addContentItem}
                          updateContentItem={updateContentItem}
                          removeContentItem={removeContentItem}
                          moveContentItem={moveContentItem}
                          analyzingItems={analyzingItems}
                          setAnalyzingItems={setAnalyzingItems}
                          selectedModel={selectedModel}
                        />
                      );
                    })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Model Selection and Generate Button */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-gray-200 space-y-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">AI Model Selection</h3>
            <AIModelSelector 
              model={selectedModel} 
              models={AI_MODELS} 
              setModel={setSelectedModel} 
            />
            
            <div className="text-center pt-8">
              <Button 
                onClick={generateAllSections} 
                disabled={!canProceed}
                size="lg"
                className="px-12 py-6 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300"
              >
                <Sparkles className="h-6 w-6 mr-3" />
                Generate Complete Research Paper
              </Button>
              <div className="mt-4">
                {!canProceed ? (
                  <p className="text-red-500 text-lg font-medium">
                    Please provide a title for your research paper
                  </p>
                ) : (
                  <p className="text-gray-600 text-base">
                    Only a title is required. Additional content will enhance the quality of generated paper.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render generation step if not in editor mode
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {isGenerating ? "Generating Your Research Paper" : "Research Paper Generated"}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {isGenerating 
              ? "Our AI is carefully crafting each section of your research paper step by step" 
              : "All sections of your research paper have been generated successfully"}
          </p>
        </div>

        <GenerationPanel 
          generatedSections={generatedSections}
          isGenerating={isGenerating}
          onExport={handleExport}
          onEditInEditor={handleEditInMainEditor}
          paperMetadata={userInputs.metadata}
          allCitations={allCitations}
          sectionCitations={sectionCitations}
        />
      </div>
    </div>
  );
}
