import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ThumbsUp, 
  ThumbsDown, 
  RefreshCw, 
  Edit, 
  Eye, 
  MessageSquare,
  FileText,
  BarChart3,
  Quote,
  Loader2
} from "lucide-react";
import { GeneratedChapter, GeneratedOutline } from '../types';

interface ChapterReviewCardProps {
  chapter: GeneratedChapter;
  chapterIndex: number;
  outline?: GeneratedOutline;
  onApprove: () => void;
  onRegenerate: (feedback?: string) => void;
  onEdit: (content: string) => void;
  isRegenerating?: boolean;
}

export const ChapterReviewCard: React.FC<ChapterReviewCardProps> = ({
  chapter,
  chapterIndex,
  outline,
  onApprove,
  onRegenerate,
  onEdit,
  isRegenerating = false
}) => {
  const [feedback, setFeedback] = useState('');
  const [editedContent, setEditedContent] = useState(chapter.content || '');
  const [isEditing, setIsEditing] = useState(false);

  // Debug logging
  console.log(`ChapterReviewCard for chapter ${chapterIndex + 1}:`, {
    hasContent: !!chapter.content,
    contentLength: chapter.content?.length || 0,
    contentPreview: chapter.content?.substring(0, 100) || 'No content',
    chapterStatus: chapter.status,
    chapterTitle: chapter.title
  });

  const handleRegenerate = () => {
    if (feedback.trim()) {
      onRegenerate(feedback);
      setFeedback('');
    } else {
      onRegenerate();
    }
  };

  const handleSaveEdit = () => {
    onEdit(editedContent);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(chapter.content || '');
    setIsEditing(false);
  };

  const wordCount = chapter.content?.split(' ').length || 0;
  const estimatedReadTime = Math.ceil(wordCount / 200); // Average reading speed

  return (
    <Card className="shadow-lg border-2 border-blue-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-600" />
            Chapter {chapterIndex + 1}: {chapter.title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {wordCount.toLocaleString()} words
            </Badge>
            <Badge variant="secondary" className="text-sm">
              ~{estimatedReadTime} min read
            </Badge>
            {chapter.citations && chapter.citations.length > 0 && (
              <Badge variant="outline" className="text-sm">
                <Quote className="h-3 w-3 mr-1" />
                {chapter.citations.length} citations
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="content" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="outline">Outline</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
          </TabsList>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-4">
            {isEditing ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Edit Chapter Content</h4>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveEdit} size="sm">
                      Save Changes
                    </Button>
                    <Button onClick={handleCancelEdit} variant="outline" size="sm">
                      Cancel
                    </Button>
                  </div>
                </div>
                <Textarea
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  rows={20}
                  className="font-mono text-sm"
                />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Generated Content</h4>
                  <Button
                    onClick={() => setIsEditing(true)}
                    variant="outline"
                    size="sm"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Content
                  </Button>
                </div>
                <ScrollArea className="h-96 border rounded-lg p-4 bg-gray-50">
                  <div className="prose max-w-none">
                    {chapter.content ? (
                      chapter.content.split('\n').map((paragraph, index) => (
                        <p key={index} className="mb-3 text-sm leading-relaxed">
                          {paragraph}
                        </p>
                      ))
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p className="text-lg font-medium">No content available</p>
                        <p className="text-sm">Chapter content has not been generated yet.</p>
                        <p className="text-xs mt-2">Status: {chapter.status}</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            )}
          </TabsContent>

          {/* Outline Tab */}
          <TabsContent value="outline" className="space-y-4">
            {outline ? (
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold mb-2">Chapter Overview</h4>
                  <p className="text-sm text-gray-700">{outline.description}</p>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {outline.keyPoints.map((point, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {point}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold">Section Structure</h4>
                  {outline.sections.map((section) => (
                    <div key={section.id} className="pl-4 border-l-2 border-blue-200">
                      <div className="font-medium text-sm">{section.title}</div>
                      <div className="text-xs text-gray-600 mb-1">{section.description}</div>
                      <div className="text-xs text-gray-500">
                        Target: {section.estimatedWordCount} words
                      </div>
                      {section.keyPoints.length > 0 && (
                        <div className="mt-1 flex flex-wrap gap-1">
                          {section.keyPoints.map((point, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {point}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No outline available for this chapter</p>
              </div>
            )}
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="stats" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">{wordCount.toLocaleString()}</div>
                <div className="text-sm text-blue-800">Words Written</div>
              </div>
              <div className="p-4 bg-green-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {outline?.estimatedWordCount.toLocaleString() || 'N/A'}
                </div>
                <div className="text-sm text-green-800">Target Words</div>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {chapter.citations?.length || 0}
                </div>
                <div className="text-sm text-purple-800">Citations</div>
              </div>
              <div className="p-4 bg-orange-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">{estimatedReadTime}</div>
                <div className="text-sm text-orange-800">Minutes to Read</div>
              </div>
            </div>

            {outline && (
              <div className="space-y-3">
                <h4 className="font-semibold">Progress vs Target</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Word Count Progress</span>
                    <span>{Math.round((wordCount / outline.estimatedWordCount) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((wordCount / outline.estimatedWordCount) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Feedback Section */}
        <div className="mt-6 space-y-4 border-t pt-4">
          <div className="space-y-3">
            <label className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Feedback for Improvement (Optional)
            </label>
            <Textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Provide specific feedback if you'd like the chapter regenerated with improvements..."
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={onApprove} 
              size="lg" 
              className="flex-1"
              disabled={isRegenerating}
            >
              <ThumbsUp className="h-4 w-4 mr-2" />
              Approve & Continue
            </Button>
            <Button 
              onClick={handleRegenerate}
              variant="outline" 
              size="lg"
              className="flex-1"
              disabled={isRegenerating}
            >
              {isRegenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Regenerating...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {feedback.trim() ? 'Regenerate with Feedback' : 'Regenerate Chapter'}
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
