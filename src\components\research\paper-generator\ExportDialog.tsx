import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Download, FileType, Printer, Edit } from "lucide-react";
import { GeneratedSection, PaperMetadata } from './types';
import { documentExportService } from './document-export.service';

interface ExportDialogProps {
  metadata: PaperMetadata;
  generatedSections: GeneratedSection[];
  onEditInEditor: () => void;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  metadata,
  generatedSections,
  onEditInEditor
}) => {
  const [exportError, setExportError] = useState<string | null>(null);

  const handleExportToWord = async () => {
    setExportError(null);
    try {
      // Generate HTML content for the paper (reuse logic from document-export.service)
      const authorsText = (metadata.authors && metadata.authors.length > 0)
        ? metadata.authors.join(', ')
        : 'Anonymous';
      const sectionsHTML = generatedSections
        .filter(section => section.status === 'completed' && section.content)
        .sort((a, b) => {
          const sectionOrder: Record<string, number> = {
            'abstract': 1,
            'introduction': 2,
            'literature-review': 3,
            'methodology': 4,
            'results': 5,
            'discussion': 6,
            'conclusion': 7
          };
          return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
        })
        .map(section => `
          <div class="section">
            <h2>${section.name}</h2>
            <div>${section.content?.replace(/\n/g, '<br/>')}</div>
          </div>
        `)
        .join('');
      const htmlContent = `
        <h1>${metadata.title}</h1>
        <div class="authors">${authorsText}</div>
        ${sectionsHTML}
      `;
      await documentExportService.exportToDocx(metadata.title, htmlContent, `${metadata.title.replace(/[^a-zA-Z0-9]/g, '_')}.docx`);
    } catch (err) {
      setExportError('Failed to export document to DOCX format. Please try again.');
      // Optionally log the error for debugging
      console.error('DOCX export error:', err);
    }
  };

  const handleExportToPDF = () => {
    documentExportService.exportToPDF(
      metadata.title,
      metadata.authors || [],
      generatedSections
    );
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          size="lg" 
          className="px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 shadow-lg"
        >
          <Download className="h-5 w-5 mr-2" />
          Export Complete Paper
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Export Research Paper</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          {exportError && (
            <div className="text-red-600 font-semibold">{exportError}</div>
          )}
          <p className="text-gray-600">
            Choose your preferred format to export your research paper.
          </p>
          
          <Button
            onClick={handleExportToWord}
            className="flex items-center gap-3 px-6 py-6 bg-blue-600 hover:bg-blue-700"
          >
            <FileType className="h-5 w-5" />
            <div className="text-left">
              <div className="font-semibold">Export to Word (.doc)</div>
              <div className="text-sm opacity-80">Compatible with Microsoft Word and other editors</div>
            </div>
          </Button>
          
          <Button
            onClick={handleExportToPDF}
            className="flex items-center gap-3 px-6 py-6 bg-purple-600 hover:bg-purple-700"
          >
            <Printer className="h-5 w-5" />
            <div className="text-left">
              <div className="font-semibold">Export to PDF</div>
              <div className="text-sm opacity-80">Generate a PDF using your browser's print functionality</div>
            </div>
          </Button>
          
          <Button
            onClick={onEditInEditor}
            variant="outline"
            className="flex items-center gap-3 px-6 py-6 border-2"
          >
            <Edit className="h-5 w-5" />
            <div className="text-left">
              <div className="font-semibold">Edit in Main Editor</div>
              <div className="text-sm opacity-80">Open in the main text editor for further refinement</div>
            </div>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
