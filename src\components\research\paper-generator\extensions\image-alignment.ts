import { Extension } from '@tiptap/core';
import { Node } from '@tiptap/pm/model';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';

export type ImageAlignmentOptions = {
  types: string[];
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    imageAlignment: {
      setImageAlignment: (alignment: 'left' | 'center' | 'right' | null) => ReturnType;
    };
  }
}

export const ImageAlignment = Extension.create<ImageAlignmentOptions>({
  name: 'imageAlignment',

  addOptions() {
    return {
      types: ['resizableImage'],
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          alignment: {
            default: null,
            renderHTML: attributes => {
              if (!attributes.alignment) {
                return {};
              }

              const style = `display: block; margin: ${
                attributes.alignment === 'center'
                  ? '0 auto'
                  : attributes.alignment === 'left'
                  ? '0 auto 0 0'
                  : '0 0 0 auto'
              };`;

              return {
                style,
              };
            },
            parseHTML: element => {
              const style = element.getAttribute('style');
              
              if (!style) return null;
              
              if (style.includes('margin: 0 auto')) return 'center';
              if (style.includes('margin: 0 auto 0 0')) return 'left';
              if (style.includes('margin: 0 0 0 auto')) return 'right';
              
              return null;
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setImageAlignment:
        (alignment) => 
        ({ tr, state, dispatch }) => {
          const { selection } = state;
          const { empty, node } = selection;
          
          if (empty && !node) {
            return false;
          }
          
          let nodeType = node?.type.name;
          
          if (!nodeType || !this.options.types.includes(nodeType)) {
            return false;
          }

          if (dispatch) {
            tr.setNodeMarkup(selection.from, undefined, {
              ...node.attrs,
              alignment,
            });
            
            return true;
          }
          
          return false;
        },
    };
  },
});

export default ImageAlignment;
