import { LucideIcon } from "lucide-react";

// Book structure and content types
export interface BookMetadata {
  title: string;
  subtitle?: string;
  genre: string;
  targetAudience: string;
  keywords: string[];
  authors: string[];
  description: string;
  estimatedLength: 'short' | 'medium' | 'long'; // 50-100, 100-300, 300+ pages
  tone: 'academic' | 'professional' | 'casual' | 'narrative';
}

export interface ChapterOutline {
  id: string;
  title: string;
  description: string;
  subSections: SubSection[];
  estimatedWordCount: number;
  order: number;
}

export interface SubSection {
  id: string;
  title: string;
  description: string;
  order: number;
  level: number; // 1 = 1.1, 2 = 1.1.1, etc.
}

export interface ContentItem {
  id: string;
  type: 'text' | 'figure';
  content: string;
  order: number;
  title?: string;
  caption?: string;
  aiAnalysis?: string;
}

export interface UserChapter {
  id: string;
  title: string;
  outline: ChapterOutline;
  items: ContentItem[];
}

export interface UserInputs {
  metadata: BookMetadata;
  userChapters: UserChapter[];
}

// Generated content types
export interface GeneratedChapter {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'outline-generating' | 'outline-ready' | 'content-generating' | 'content-ready' | 'completed' | 'error';
  icon: LucideIcon;
  content?: string;
  summary?: string; // For context management
  wordCount?: number;
  citations?: string[];
  order: number;
  subSections?: GeneratedSubSection[];
  outline?: GeneratedOutline; // Generated outline for the chapter
  userApproved?: boolean; // Whether user approved this chapter
}

export interface GeneratedSubSection {
  id: string;
  title: string;
  content: string;
  level: number;
  order: number;
  wordCount: number;
}

// Generated outline types
export interface GeneratedOutline {
  id: string;
  chapterId: string;
  title: string;
  description: string;
  sections: GeneratedOutlineSection[];
  estimatedWordCount: number;
  keyPoints: string[];
  status: 'generating' | 'ready' | 'approved' | 'needs-revision';
}

export interface GeneratedOutlineSection {
  id: string;
  title: string;
  description: string;
  level: number; // 1 = main section, 2 = subsection, etc.
  order: number;
  estimatedWordCount: number;
  keyPoints: string[];
}

// Context management types
export interface ChapterContext {
  chapterId: string;
  summary: string;
  keyPoints: string[];
  wordCount: number;
  generatedAt: Date;
}

export interface BookContext {
  bookOutline: string;
  previousChapters: ChapterContext[];
  currentChapter: string;
  totalWordCount: number;
  maxContextTokens: number;
}

// Workflow state types
export interface BookGenerationWorkflow {
  currentStep: 'metadata' | 'chapters' | 'outline-generation' | 'outline-review' | 'chapter-generation' | 'chapter-review' | 'completed';
  currentChapterIndex: number;
  totalChapters: number;
  outlineGenerated: boolean;
  chaptersCompleted: number;
}

export interface ChapterGenerationState {
  chapterId: string;
  status: 'pending' | 'generating-outline' | 'outline-ready' | 'generating-content' | 'content-ready' | 'approved' | 'error';
  canProceed: boolean;
  needsRegeneration: boolean;
  userFeedback?: string;
}

// Book-specific section types
export interface BookSectionType {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
  description: string;
  order: number;
  required?: boolean;
  isChapter?: boolean;
}

// AI generation types (extending paper types)
export interface AIGenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  stopSequences?: string[];
  context?: BookContext;
}

export interface AIAnalysisResult {
  content?: string;
  confidence?: number;
  tokens?: number;
  summary?: string;
  keyPoints?: string[];
}

// Citation types (reusing from paper generator)
export interface Citation {
  id: string;
  inTextFormat: string;
  authors: string[];
  year: number;
  title: string;
  source: string;
  doi?: string;
  url?: string;
  chapterIds: string[];
  referenceText?: string;
}

// Export and formatting types
export interface BookExportOptions {
  format: 'docx' | 'pdf' | 'epub' | 'html';
  includeTableOfContents: boolean;
  includeGlossary: boolean;
  includeIndex: boolean;
  includeBibliography: boolean;
  chapterNumbering: 'numeric' | 'roman' | 'none';
  pageNumbering: boolean;
}

// Glossary and index types
export interface GlossaryTerm {
  id: string;
  term: string;
  definition: string;
  chapterReferences: string[];
}

export interface IndexEntry {
  id: string;
  term: string;
  subTerms?: string[];
  pageReferences: number[];
  chapterReferences: string[];
}
