import { AIGenerationOptions, AIAnalysisResult } from './types';

/**
 * Service for handling AI text and image analysis requests
 */
export class PaperAIService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }
  
  /**
   * Analyze text using the specified AI model
   */
  async analyzeText(
    prompt: string, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No response]';
    } catch (error: any) {
      console.error('AI Text Analysis Error:', error);
      throw new Error('Failed to generate text analysis. ' + (error?.message || 'Please try again.'));
    }
  }
  
  /**
   * Analyze an image using the specified AI model
   */
  async analyzeImage(
    imageUrl: string, 
    prompt: string, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;
      
      const dataUrl = await this.convertImageToBase64(imageUrl);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: prompt },
                { type: "image_url", image_url: { url: dataUrl } }
              ]
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No analysis available]';
    } catch (error: any) {
      console.error('AI Image Analysis Error:', error);
      throw new Error('Failed to analyze image. ' + (error?.message || 'Please try again.'));
    }
  }
  
  /**
   * Generate a complete research paper section based on prompts and previous sections
   */
  async generatePaperSection(
    sectionPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "anthropic/claude-3.5-sonnet", maxTokens = 1024 } = options;
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are a helpful academic writing assistant that generates well-structured, scholarly research paper sections. Use formal academic language, cite sources appropriately, and organize content logically."
            },
            {
              role: "user",
              content: sectionPrompt
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[Failed to generate section]';
    } catch (error: any) {
      console.error('AI Section Generation Error:', error);
      throw new Error('Failed to generate paper section. ' + (error?.message || 'Please try again.'));
    }
  }
  
  /**
   * Convert an image URL to a base64 data URL
   */
  private async convertImageToBase64(url: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.onload = function() {
        const reader = new FileReader();
        reader.onloadend = function() {
          resolve(reader.result as string);
        };
        reader.onerror = reject;
        reader.readAsDataURL(xhr.response);
      };
      xhr.onerror = reject;
      xhr.open('GET', url);
      xhr.responseType = 'blob';
      xhr.send();
    });
  }
}

export const paperAIService = new PaperAIService();

export default paperAIService;
