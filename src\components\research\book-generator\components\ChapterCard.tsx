import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  X, 
  GripVertical, 
  BookOpen,
  FileText,
  ImageIcon,
  Edit3,
  Trash2
} from "lucide-react";
import { UserChapter, ChapterOutline, SubSection, ContentItem } from '../types';
import { ContentItemRenderer } from './ContentItemRenderer';
import { WORD_COUNT_ESTIMATES } from '../constants';

interface ChapterCardProps {
  chapter: UserChapter;
  chapterIndex: number;
  onChapterUpdate: (chapterId: string, updates: Partial<UserChapter>) => void;
  onChapterRemove: (chapterId: string) => void;
  onMoveChapter: (chapterId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: (items: Set<string>) => void;
  selectedModel: string;
}

export const ChapterCard: React.FC<ChapterCardProps> = ({
  chapter,
  chapterIndex,
  onChapterUpdate,
  onChapterRemove,
  onMoveChapter,
  analyzingItems,
  setAnalyzingItems,
  selectedModel
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditingOutline, setIsEditingOutline] = useState(false);
  const [newSubSectionTitle, setNewSubSectionTitle] = useState('');

  const updateChapterField = (field: keyof UserChapter, value: any) => {
    onChapterUpdate(chapter.id, { [field]: value });
  };

  const updateOutlineField = (field: keyof ChapterOutline, value: any) => {
    updateChapterField('outline', { ...chapter.outline, [field]: value });
  };

  const addSubSection = () => {
    if (newSubSectionTitle.trim()) {
      const newSubSection: SubSection = {
        id: `subsection-${Date.now()}`,
        title: newSubSectionTitle.trim(),
        description: '',
        order: chapter.outline.subSections.length + 1,
        level: 1
      };
      
      updateOutlineField('subSections', [...chapter.outline.subSections, newSubSection]);
      setNewSubSectionTitle('');
    }
  };

  const updateSubSection = (subSectionId: string, updates: Partial<SubSection>) => {
    const updatedSubSections = chapter.outline.subSections.map(sub =>
      sub.id === subSectionId ? { ...sub, ...updates } : sub
    );
    updateOutlineField('subSections', updatedSubSections);
  };

  const removeSubSection = (subSectionId: string) => {
    const updatedSubSections = chapter.outline.subSections.filter(sub => sub.id !== subSectionId);
    updateOutlineField('subSections', updatedSubSections);
  };

  const addContentItem = (type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: `item-${Date.now()}`,
      type,
      content: '',
      order: chapter.items.length + 1,
      title: type === 'figure' ? 'Figure Title' : undefined,
      caption: type === 'figure' ? 'Figure caption...' : undefined
    };
    
    updateChapterField('items', [...chapter.items, newItem]);
  };

  const updateContentItem = (itemId: string, updates: Partial<ContentItem>) => {
    const updatedItems = chapter.items.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    );
    updateChapterField('items', updatedItems);
  };

  const removeContentItem = (itemId: string) => {
    const updatedItems = chapter.items.filter(item => item.id !== itemId);
    updateChapterField('items', updatedItems);
  };

  const moveContentItem = (itemId: string, direction: 'up' | 'down') => {
    const items = [...chapter.items];
    const index = items.findIndex(item => item.id === itemId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= items.length) return;
    
    [items[index], items[newIndex]] = [items[newIndex], items[index]];
    
    // Update order values
    items.forEach((item, idx) => {
      item.order = idx + 1;
    });
    
    updateChapterField('items', items);
  };

  const estimatedWordCount = WORD_COUNT_ESTIMATES.chapter;

  return (
    <Card className="shadow-lg border-2 border-gray-200 hover:border-blue-300 transition-all duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
            <GripVertical className="h-5 w-5 text-gray-400" />
            <BookOpen className="h-6 w-6 text-blue-600" />
            <div>
              <CardTitle className="text-xl">
                Chapter {chapterIndex + 1}: {chapter.outline.title || 'Untitled Chapter'}
              </CardTitle>
              <p className="text-sm text-gray-500 mt-1">
                {chapter.outline.subSections.length} sections • {chapter.items.length} content items
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {estimatedWordCount.min}-{estimatedWordCount.max} words
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveChapter(chapter.id, 'up')}
              disabled={chapterIndex === 0}
            >
              ↑
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveChapter(chapter.id, 'down')}
            >
              ↓
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onChapterRemove(chapter.id)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Chapter Basic Info */}
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Chapter Title</label>
                <Input
                  value={chapter.outline.title}
                  onChange={(e) => updateOutlineField('title', e.target.value)}
                  placeholder="Enter chapter title..."
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Estimated Word Count</label>
                <Input
                  type="number"
                  value={chapter.outline.estimatedWordCount}
                  onChange={(e) => updateOutlineField('estimatedWordCount', parseInt(e.target.value) || 0)}
                  placeholder="3000"
                  min="500"
                  max="10000"
                />
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Chapter Description</label>
              <Textarea
                value={chapter.outline.description}
                onChange={(e) => updateOutlineField('description', e.target.value)}
                placeholder="Describe what this chapter will cover, its main objectives, and key points..."
                rows={3}
              />
            </div>
          </div>

          {/* Sub-sections */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-lg">Chapter Outline</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingOutline(!isEditingOutline)}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                {isEditingOutline ? 'Done' : 'Edit Outline'}
              </Button>
            </div>

            {isEditingOutline && (
              <div className="flex gap-2">
                <Input
                  value={newSubSectionTitle}
                  onChange={(e) => setNewSubSectionTitle(e.target.value)}
                  placeholder="Add new section..."
                  onKeyPress={(e) => e.key === 'Enter' && addSubSection()}
                />
                <Button onClick={addSubSection} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}

            <div className="space-y-3">
              {chapter.outline.subSections.map((subSection, index) => (
                <div key={subSection.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-500">
                          {chapterIndex + 1}.{index + 1}
                        </span>
                        {isEditingOutline ? (
                          <Input
                            value={subSection.title}
                            onChange={(e) => updateSubSection(subSection.id, { title: e.target.value })}
                            className="font-medium"
                          />
                        ) : (
                          <span className="font-medium">{subSection.title}</span>
                        )}
                      </div>
                      {isEditingOutline ? (
                        <Textarea
                          value={subSection.description}
                          onChange={(e) => updateSubSection(subSection.id, { description: e.target.value })}
                          placeholder="Describe this section..."
                          rows={2}
                        />
                      ) : (
                        subSection.description && (
                          <p className="text-sm text-gray-600">{subSection.description}</p>
                        )
                      )}
                    </div>
                    {isEditingOutline && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSubSection(subSection.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Content Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-lg">Chapter Content</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addContentItem('text')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Add Text
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addContentItem('figure')}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Add Figure
                </Button>
              </div>
            </div>

            {chapter.items.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No content items yet. Add text or figures to provide context for this chapter.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {chapter.items.map((item, index) => (
                  <ContentItemRenderer
                    key={item.id}
                    sectionId={chapter.id}
                    sectionName={chapter.outline.title}
                    item={item}
                    index={index}
                    totalItems={chapter.items.length}
                    updateContentItem={updateContentItem}
                    removeContentItem={removeContentItem}
                    moveContentItem={moveContentItem}
                    analyzingItems={analyzingItems}
                    setAnalyzingItems={setAnalyzingItems}
                    selectedModel={selectedModel}
                    textPlaceholders={{ [chapter.outline.title]: "Describe the main concepts and insights for this chapter..." }}
                    figurePlaceholders={{ [chapter.outline.title]: "Explain how this visual supports the chapter's concepts..." }}
                    defaultTextPrompt="Provide detailed content for this chapter section..."
                    defaultFigurePrompt="Describe how this figure illustrates the chapter's key concepts..."
                  />
                ))}
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};
