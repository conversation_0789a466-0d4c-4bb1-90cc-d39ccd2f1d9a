import { LucideIcon } from "lucide-react";

// Paper structure and content types
export interface PaperMetadata {
  title: string;
  researchField: string;
  keywords: string[];
  authors: string[];
}

export interface ContentItem {
  id: string;
  type: 'text' | 'figure';
  content: string;
  order: number;
  title?: string;
  caption?: string;
  aiAnalysis?: string;
}

export interface UserSection {
  id: string;
  name: string;
  items: ContentItem[];
}

export interface UserInputs {
  metadata: PaperMetadata;
  userSections: UserSection[];
}

export interface GeneratedSection {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  icon: LucideIcon;
  content?: string;
  citations?: string[];     // Array of citation IDs found in this section
}

export interface SectionType {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
  description: string;
  order: number;
  required?: boolean;
}

// AI generation types
export interface AIModelOption {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  maxTokens: number;
}

export interface AIGenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  stopSequences?: string[];
}

export interface AIAnalysisResult {
  content: string;
  confidence: number;
  tokens: number;
}

/**
 * Structure for a citation extracted from paper content
 */
export interface Citation {
  id: string;           // Unique ID for the citation
  inTextFormat: string; // How it appears in text, e.g., "(Smith, 2023)"
  authors: string[];    // List of author names
  year: number;         // Publication year
  title: string;        // Title of the work (when available)
  source: string;       // Journal/conference/book name (when available)
  doi?: string;         // DOI if available
  url?: string;         // URL if available
  sectionIds: string[]; // IDs of sections where this citation appears
  referenceText?: string; // Full formatted reference text
}
