import React from 'react';
import { X, FileText, ImageIcon, GripVertical } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { UserSection, ContentItem } from "./types";
import { ContentItemRenderer } from "./ContentItemRenderer";
import { EMPTY_SECTION_PROMPTS, TEXT_PLACEHOLDERS, FIGURE_PLACEHOLDERS, DEFAULT_TEXT_PROMPT, DEFAULT_FIGURE_PROMPT } from "./constants";

interface SectionCardProps {
  section: UserSection;
  sectionColor: string;
  sectionIcon: React.ElementType;
  sectionDescription: string;
  removeUserSection: (sectionId: string) => void;
  addContentItem: (sectionId: string, type: 'text' | 'figure') => void;
  updateContentItem: (sectionId: string, itemId: string, updates: Partial<ContentItem>) => void;
  removeContentItem: (sectionId: string, itemId: string) => void;
  moveContentItem: (sectionId: string, itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedModel: string;
}

export const SectionCard: React.FC<SectionCardProps> = ({
  section,
  sectionColor,
  sectionIcon: Icon,
  sectionDescription,
  removeUserSection,
  addContentItem,
  updateContentItem,
  removeContentItem,
  moveContentItem,
  analyzingItems,
  setAnalyzingItems,
  selectedModel
}) => {
  return (
    <div className="bg-gradient-to-br from-white to-gray-50/50 border-2 border-gray-200 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
      {/* Enhanced Section Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className={`w-4 h-4 rounded-full ${sectionColor} shadow-sm`}></div>
          <div>
            <h3 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <Icon className="h-6 w-6" />
              {section.name}
            </h3>
            <p className="text-gray-600 mt-1">{sectionDescription}</p>
          </div>
          <Badge variant="outline" className="text-sm px-3 py-1 bg-white/70 backdrop-blur-sm">
            {section.items.length} item{section.items.length !== 1 ? 's' : ''}
          </Badge>
        </div>
        
        {/* Enhanced Section Actions */}
        <div className="flex items-center gap-3">
          <Button 
            onClick={() => addContentItem(section.id, 'text')} 
            variant="outline" 
            className="flex items-center gap-2 hover:bg-blue-50 border-2 hover:border-blue-300 px-4 py-2"
          >
            <FileText className="h-4 w-4" />
            Add Analysis
          </Button>
          <Button 
            onClick={() => addContentItem(section.id, 'figure')} 
            variant="outline" 
            className="flex items-center gap-2 hover:bg-green-50 border-2 hover:border-green-300 px-4 py-2"
          >
            <ImageIcon className="h-4 w-4" />
            Add Figure
          </Button>
          <Button 
            onClick={() => removeUserSection(section.id)} 
            variant="ghost" 
            className="text-red-500 hover:bg-red-50 hover:text-red-600 p-2"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {section.items.length === 0 ? (
        <div className="text-center py-16 border-2 border-dashed border-gray-300 rounded-2xl bg-gradient-to-br from-gray-50/50 to-white">
          <div className="mb-6">
            <Icon className="h-16 w-16 mx-auto text-gray-300" />
          </div>
          <h4 className="text-xl font-semibold text-gray-600 mb-2">Ready to add content?</h4>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            {EMPTY_SECTION_PROMPTS[section.name] || 'Add text analysis or figures with detailed interpretations to build comprehensive research content.'}
          </p>
          <div className="flex justify-center gap-4">
            <Button 
              onClick={() => addContentItem(section.id, 'text')}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-6 py-3"
            >
              <FileText className="h-4 w-4" />
              Add Text Analysis
            </Button>
            <Button 
              onClick={() => addContentItem(section.id, 'figure')}
              variant="outline"
              className="flex items-center gap-2 border-2 hover:border-green-400 px-6 py-3"
            >
              <ImageIcon className="h-4 w-4" />
              Add Figure & Analysis
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-8">
          {section.items.map((item, index) => (
            <ContentItemRenderer
              key={item.id}
              sectionId={section.id}
              sectionName={section.name}
              item={item}
              index={index}
              totalItems={section.items.length}
              updateContentItem={updateContentItem}
              removeContentItem={removeContentItem}
              moveContentItem={moveContentItem}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
              textPlaceholders={TEXT_PLACEHOLDERS}
              figurePlaceholders={FIGURE_PLACEHOLDERS}
              defaultTextPrompt={DEFAULT_TEXT_PROMPT}
              defaultFigurePrompt={DEFAULT_FIGURE_PROMPT}
            />
          ))}
        </div>
      )}
    </div>
  );
};
